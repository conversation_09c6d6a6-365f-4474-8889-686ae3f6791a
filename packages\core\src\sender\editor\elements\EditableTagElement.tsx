import React, { useMemo } from "react";
import { RenderElementProps, useFocused, useSelected } from "slate-react";

export interface EditableTagElementProps {
  placeholder?: string;
}

// EditableTag Element Component
export const EditableTagElement: React.FC<RenderElementProps> = ({ children, element }) => {
  const selected = useSelected();
  const focused = useFocused();
  const { placeholder } = element as EditableTagElementProps;

  // Check if the element has content
  const hasContent = element.children.some((child) => child.text && child.text.replace("&#xFEFF;", "").trim() !== "");

  const placeholderVisible = !hasContent && placeholder;

  // Calculate the minimum width based on the placeholder text
  const minWidth = useMemo(() => {
    const divElement = document.createElement("div");
    divElement.style.position = "absolute";
    divElement.style.whiteSpace = "pre";
    divElement.style.visibility = "hidden";
    divElement.style.width = "auto";
    divElement.style.height = "auto";
    divElement.style.padding = "4px 8px";
    divElement.style.margin = "0";
    divElement.style.border = "1px";
    divElement.style.outline = "none";
    divElement.style.fontSize = "14px";

    document.body.appendChild(divElement);
    divElement.innerHTML = placeholder ?? "";
    const width = divElement.offsetWidth;
    document.body.removeChild(divElement);
    // TODO 偏移原因未找到
    return width + 2;
  }, [placeholder]);

  return (
    <span
      style={{
        display: "inline-block",
        margin: "0 4px",
        padding: "3px 8px",
        borderRadius: "4px",
        backgroundColor: "rgba(11,104,230,0.1)",
        color: "#0b68e6",
        border: selected && focused ? "1px solid #0B68E6" : "1px solid rgba(0,0,0,0)",
        position: "relative",
        lineHeight: "1.2em",
        minWidth: placeholderVisible ? `${minWidth}px` : "auto",
      }}
      contentEditable={true}
    >
      {placeholderVisible && (
        <span
          style={{
            width: 0,
            height: 0,
            overflow: "visible",
            position: "relative",
          }}
        >
          <span
            contentEditable={false}
            style={{
              left: 0,
              top: 0,
              position: "absolute",
              width: "max-content",
              color: "rgba(11,104,230,0.35)",
              pointerEvents: "none",
              display: "inline-block",
              userSelect: "none",
            }}
          >
            {placeholder}
          </span>
        </span>
      )}
      {children}
      <span contentEditable={false}>&#xFEFF;</span>
    </span>
  );
};

export default EditableTagElement;
