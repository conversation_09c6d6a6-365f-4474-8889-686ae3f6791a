/**
 * Request cancellation example
 *
 * 演示如何使用AbortSignal取消请求
 */

import { Request } from "../request";

// 创建请求实例
const request = new Request({
  baseURL: "https://api.example.com",
  timeout: 10000,
});

/**
 * 使用AbortController取消请求示例
 */
async function cancelRequestWithAbortController() {
  console.log("使用AbortController取消请求示例:");

  // 创建AbortController
  const controller = new AbortController();

  // 设置超时自动取消
  setTimeout(() => {
    console.log("请求超时，自动取消");
    controller.abort("请求超时");
  }, 2000);

  try {
    // 发送请求，传入signal
    console.log("发送请求...");
    const response = await request.get("/users", null, {
      signal: controller.signal,
    });
    console.log("请求成功:", response);
  } catch (error: any) {
    if (error.name === "AbortError" || error.message === "canceled") {
      console.log("请求已被取消");
    } else {
      console.error("请求失败:", error);
    }
  }
}

/**
 * 使用请求ID取消请求示例
 */
async function cancelRequestWithRequestId() {
  console.log("\n使用请求ID取消请求示例:");

  // 自定义请求ID
  const requestId = "get-users-request";

  // 设置超时自动取消
  setTimeout(() => {
    console.log("请求超时，通过ID取消");
    request.cancelRequest(requestId, "请求超时");
  }, 2000);

  try {
    // 发送请求，传入requestId
    console.log("发送请求...");
    const response = await request.get("/users", null, {
      requestId,
    });
    console.log("请求成功:", response);
  } catch (error: any) {
    if (error.name === "AbortError" || error.message === "canceled") {
      console.log("请求已被取消");
    } else {
      console.error("请求失败:", error);
    }
  }
}

/**
 * 取消多个请求示例
 */
async function cancelMultipleRequests() {
  console.log("\n取消多个请求示例:");

  // 创建多个请求
  const requests = [
    request.get("/users", null, { requestId: "req-1" }),
    request.get("/posts", null, { requestId: "req-2" }),
    request.get("/comments", null, { requestId: "req-3" }),
  ];

  // 设置超时自动取消所有请求
  setTimeout(() => {
    console.log("取消所有请求");
    request.cancelAllRequests("用户取消了操作");
  }, 1000);

  try {
    // 等待所有请求完成
    console.log("发送多个请求...");
    await Promise.all(requests);
    console.log("所有请求成功完成");
  } catch (error: any) {
    if (error.name === "AbortError" || error.message === "canceled") {
      console.log("请求已被取消");
    } else {
      console.error("请求失败:", error);
    }
  }
}

// 运行示例
async function runExamples() {
  await cancelRequestWithAbortController();
  await cancelRequestWithRequestId();
  await cancelMultipleRequests();
}

// 执行示例
runExamples().catch(console.error);
