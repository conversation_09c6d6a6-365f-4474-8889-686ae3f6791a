/**
 * Request module
 *
 * 定义请求模块的功能和接口
 */

import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from "axios";
import { nanoid } from "nanoid";

import { StandardErrorResponse } from "@/types";

import { RequestCancelManager } from "./cancel";
import { createRequestConfig } from "./config";
import { defaultErrorHandler } from "./error";
import { applyRequestInterceptors, applyResponseInterceptors } from "./interceptors";
import { RequestConfig, RequestOptions } from "./types";

/**
 * 请求服务类
 * 封装Axios，提供统一的请求接口
 */
export class Request {
  /** Axios实例 */
  private axiosInstance: AxiosInstance;
  /** 请求取消管理器 */
  private cancelManager: RequestCancelManager;
  /** 请求配置 */
  private config: RequestConfig;

  /**
   * 构造函数
   * @param config 请求配置
   */
  constructor(config?: RequestConfig) {
    // 创建配置
    this.config = createRequestConfig(config);

    // 创建Axios实例
    this.axiosInstance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
    });

    // 创建请求取消管理器
    this.cancelManager = new RequestCancelManager();

    // 应用拦截器
    this.setupInterceptors();
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    // 应用请求拦截器
    applyRequestInterceptors(this.axiosInstance, this.config.requestInterceptors);

    // 应用响应拦截器
    applyResponseInterceptors(this.axiosInstance, this.config.responseInterceptors);
  }

  /**
   * 处理请求配置
   * @param options 请求选项
   * @returns 处理后的Axios请求配置
   */
  private processRequestOptions(options: RequestOptions = {}): AxiosRequestConfig & {} {
    const { requestId, signal: externalSignal, ...axiosOptions } = options;

    // 如果提供了外部信号，直接使用; 否则，创建新的AbortController
    if (externalSignal) {
      return {
        ...axiosOptions,
        signal: externalSignal,
      };
    } else {
      // 生成请求ID，如果没有提供则自动生成
      const id = requestId || nanoid();
      const { signal } = this.cancelManager.addRequest(id);

      // 将请求ID存储在axiosOptions的自定义属性中，以便后续使用
      const config = {
        ...axiosOptions,
        signal,
      };

      // 使用非标准属性存储requestId，以便在拦截器中使用
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (config as any).requestId = id;

      return config;
    }
  }

  /**
   * 获取Axios实例
   * @returns Axios实例
   */
  public getAxiosInstance(): AxiosInstance {
    return this.axiosInstance;
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param options 请求选项
   * @returns 响应数据
   */
  public async get<T>(url: string, params?: unknown, options?: RequestOptions) {
    const config = this.processRequestOptions({
      ...options,
      params,
      method: "GET",
      url,
    });

    try {
      return await this.axiosInstance.request<T>(config);
    } catch (error) {
      return this.handleRequestError(error as AxiosError, options);
    }
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   * @returns 响应数据
   */
  public async post<T>(url: string, data?: unknown, options?: RequestOptions) {
    const config = this.processRequestOptions({
      ...options,
      data,
      method: "POST",
      url,
    });

    try {
      return await this.axiosInstance.request<T>(config);
    } catch (error) {
      return this.handleRequestError(error as AxiosError, options);
    }
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   * @returns 响应数据
   */
  public async put<T>(url: string, data?: unknown, options?: RequestOptions) {
    const config = this.processRequestOptions({
      ...options,
      data,
      method: "PUT",
      url,
    });

    try {
      return await this.axiosInstance.request<T>(config);
    } catch (error) {
      return this.handleRequestError(error as AxiosError, options);
    }
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param params 请求参数
   * @param options 请求选项
   * @returns 响应数据
   */
  public async delete<T>(url: string, params?: unknown, options?: RequestOptions) {
    const config = this.processRequestOptions({
      ...options,
      params,
      method: "DELETE",
      url,
    });

    try {
      return await this.axiosInstance.request<T>(config);
    } catch (error) {
      return this.handleRequestError(error as AxiosError, options);
    }
  }

  /**
   * 发送PATCH请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   * @returns 响应数据
   */
  public async patch<T>(url: string, data?: unknown, options?: RequestOptions) {
    const config = this.processRequestOptions({
      ...options,
      data,
      method: "PATCH",
      url,
    });

    try {
      return await this.axiosInstance.request<T>(config);
    } catch (error) {
      return this.handleRequestError(error as AxiosError, options);
    }
  }

  /**
   * 发送请求
   * @param options 请求选项
   * @returns 响应数据
   */
  public async request<T>(options: RequestOptions) {
    const config = this.processRequestOptions(options);

    try {
      return await this.axiosInstance.request<T>(config);
    } catch (error) {
      return this.handleRequestError(error as AxiosError, options);
    }
  }

  /**
   * 处理请求错误
   * @param error 错误对象
   * @param options 请求选项
   * @returns 错误处理结果
   */
  private handleRequestError(error: AxiosError, options?: RequestOptions) {
    // 使用自定义错误处理器或默认错误处理器
    const errorHandler = this.config.errorHandler || defaultErrorHandler;

    // 如果配置了显示错误，可以在这里添加错误提示逻辑
    if (options?.showError !== false) {
      // 这里可以添加全局错误提示，例如使用antd的message组件
      // message.error(error.message);
    }

    return errorHandler(error as AxiosError<StandardErrorResponse>);
  }

  /**
   * 取消所有请求
   * @param message 取消消息
   */
  public cancelAllRequests(message?: string): void {
    this.cancelManager.cancelAllRequests(message);
  }

  /**
   * 取消指定请求
   * @param requestId 请求ID
   * @param message 取消消息
   */
  public cancelRequest(requestId: string, message?: string): void {
    this.cancelManager.cancelRequest(requestId, message);
  }
}
