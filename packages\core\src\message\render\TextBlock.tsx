import React, { useContext, useMemo } from "react";
import Markdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

import CodeViewer from "./CoderViewer";
import MermaidRenderer from "./MermaidRenderer";

interface TextBlockProps {
  content: string;
}

const MarkdownContext = React.createContext<{
  content: string;
}>({
  content: "",
});

const Code: React.FC<any> = (props) => {
  const { className, children, node, ...rest } = props;
  const match = /language-(\w+)/.exec(className || "");
  const language = match ? match[1] : "";
  const { content } = useContext(MarkdownContext);
  const start = node.position.start.offset;
  const end = node.position.end.offset;

  const rawCodeStr = useMemo(() => {
    return content.slice(start, end);
  }, [start, end, content]);

  return (
    <>
      <span hidden={language !== "mermaid"}>
        <MermaidRenderer code={rawCodeStr} />
      </span>
      <span hidden={language === "mermaid"}>
        <CodeViewer code={rawCodeStr} language={language} className={className} {...rest}>
          {children}
        </CodeViewer>
      </span>
    </>
  );
};

const components = {
  code: Code,
};
const rehypePlugins = [rehypeHighlight, rehypeRaw];
const remarkPlugins = [remarkGfm];

const TextBlock: React.FC<TextBlockProps> = (props) => {
  const { content } = props;

  return (
    <div className="cscs-agent-text-block ag:break-all ag:leading-[1.6em]">
      <MarkdownContext.Provider value={{ content }}>
        <Markdown rehypePlugins={rehypePlugins} remarkPlugins={remarkPlugins} components={components}>
          {content}
        </Markdown>
      </MarkdownContext.Provider>
    </div>
  );
};

export default TextBlock;
