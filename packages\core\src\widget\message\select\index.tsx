import { Select, Spin } from "antd";
import React, { useCallback, useContext, useEffect, useRef, useState } from "react";

import { useCommandRunner } from "@/command";
import { useActiveAgentCode, useActiveConversationId } from "@/core";
import { MessageContext } from "@/message";
import { get, post } from "@/request";
import { BuildInCommand } from "@/types";

/**
 * 接口配置
 */
interface ApiConfig {
  /** 接口地址 */
  url: string;
  /** 请求方法 */
  method?: "GET" | "POST";
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求参数 */
  params?: Record<string, unknown>;
  /** 请求体数据（用于 POST/PUT/PATCH） */
  data?: Record<string, unknown>;
}

/**
 * 接口返回的选项数据结构
 */
interface ApiOptionResponse {
  /** 或者直接返回选项数组 */
  data?: { label: string; value: string }[];
  /** 或者其他可能的数据结构 */
  [key: string]: unknown;
}

interface SelectInteractiveWidgetProps {
  /** 静态选项数据 */
  options?: { label: string; value: string }[];
  /** 接口配置 */
  apiConfig?: ApiConfig;
  /** 占位符 */
  placeholder?: string;
  /** 默认值 */
  defaultValue?: string;
  /** 发送后是否禁用 */
  disabledAfterSended?: boolean;
}

const SelectInteractiveWidget: React.FC<SelectInteractiveWidgetProps> = (props) => {
  const { options: staticOptions, apiConfig, placeholder, defaultValue, disabledAfterSended = true } = props;
  const [value, setValue] = useState<string | undefined>(defaultValue);
  const [apiOptions, setApiOptions] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [activeAgentCode] = useActiveAgentCode();
  const [disabled, setDisabled] = useState(false);
  const messageContext = useContext(MessageContext);

  const isFreshMessage = messageContext?.message.isFresh;

  // 如果提供了接口配置，则使用接口数据，否则使用静态数据
  const allOptions = apiConfig ? apiOptions : staticOptions || [];

  // 从接口获取选项数据
  const fetchOptionsFromApi = useCallback(async () => {
    if (!apiConfig) return;

    try {
      setLoading(true);
      setError(null);

      // 创建 AbortController 用于取消请求
      const controller = new AbortController();
      abortControllerRef.current = controller;

      const { url, method = "GET", headers, params, data } = apiConfig;

      let response;
      switch (method.toUpperCase()) {
        case "GET":
          response = await get<ApiOptionResponse>(url, params, {
            headers,
            signal: controller.signal,
          });
          break;
        case "POST":
          response = await post<ApiOptionResponse>(url, data, {
            headers,
            params,
            signal: controller.signal,
          });
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      // 解析响应数据，支持多种数据结构
      const responseData = response.data.data;
      let options: { label: string; value: string }[] = [];

      if (Array.isArray(responseData)) {
        // 直接返回数组
        options = responseData;
      }

      setApiOptions(options);
    } catch (err: unknown) {
      // 如果是取消请求，不设置错误状态
      if ((err as Error).name === "AbortError" || (err as Error).name === "CanceledError") {
        return;
      }
      console.error("Failed to fetch options from API:", err);
      setError((err as Error).message || "Failed to fetch options");
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [apiConfig]);

  const onChange = (selectedValue: string) => {
    setValue(selectedValue);
    const message = `${selectedValue}`;
    runner(BuildInCommand.SendMessage, { message, conversationId: activeConversationId, agentCode: activeAgentCode });

    if (disabledAfterSended) {
      setDisabled(true);
    }
  };

  // 组件挂载时获取接口数据
  useEffect(() => {
    fetchOptionsFromApi();

    // 组件卸载时取消请求
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [apiConfig]);

  useEffect(() => {
    if (isFreshMessage) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [isFreshMessage]);

  return (
    <div className="ag:pt-2">
      <Select
        options={allOptions}
        onChange={onChange}
        value={value}
        disabled={disabled || loading}
        placeholder={loading ? "加载中..." : placeholder}
        loading={loading}
        notFoundContent={loading ? <Spin size="small" /> : error ? `加载失败: ${error}` : "暂无数据"}
        style={{
          width: "100%",
          minWidth: 200,
        }}
      />
    </div>
  );
};

export default SelectInteractiveWidget;

// 导出类型定义供外部使用
export type { ApiConfig, SelectInteractiveWidgetProps };
