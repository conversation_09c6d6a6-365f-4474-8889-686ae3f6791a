import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";

// interface SenderHeaderProps {}

const SenderHeader: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  const widgets = useMemo(() => {
    return agentConfig?.sender?.slots?.header?.widgets ?? [];
  }, [agentConfig]);

  return (
    <div className="ag:pb-2 ag:flex ag:flex-wrap">
      {widgets.map((Widget, index) => (
        <div className="ag:mr-2 ag:mt-2" key={index}>
          <Widget.component {...Widget.props} />
        </div>
      ))}
    </div>
  );
};

export default SenderHeader;
