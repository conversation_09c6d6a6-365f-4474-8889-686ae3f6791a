# MessageReceiver 类文档

## 概述

`MessageReceiver` 类是消息处理系统的核心组件，它扩展了 `EventEmitter` 以提供实时流式消息接收功能。它处理服务器发送事件（SSE）流，解析消息块，验证数据完整性，并组装完整的消息包供应用程序使用。

### 主要特性

- **流处理**: 处理来自HTTP响应的ReadableStream数据
- **块管理**: 处理单个消息块并将其组装成完整的包
- **数据验证**: 通过块排序和完整性验证确保消息完整性
- **事件驱动架构**: 为消息处理的不同阶段发出事件
- **错误处理**: 全面的错误检测和报告
- **实时更新**: 支持部分内容更新的流式显示

## 架构

MessageReceiver 基于两级数据结构运行：

1. **块（Chunks）**: 携带消息内容和元数据的基本单元（package_id、chunk_id、is_last等）
2. **包（Packages）**: 由一个或多个块组装而成的完整消息单元

### 数据流

```
ReadableStream → 原始SSE数据 → 解析的块 → 验证的包 → 事件
```

## 构造函数

### `constructor(response: ReadableStream)`

创建一个新的MessageReceiver实例。

**参数:**
- `response: ReadableStream` - 包含SSE格式消息数据的可读流

**初始化:**
- 设置用于流处理的ReadableStreamDefaultReader
- 初始化用于数据转换的UTF-8 TextDecoder
- 继承EventEmitter的事件处理能力

**示例:**
```typescript
const stream = response.body; // 来自fetch或axios响应
const receiver = new MessageReceiver(stream);
```

## 公共方法

### `receive(): void`

开始消息接收过程。此方法启动从流中连续读取，直到完成或出错。

**行为:**
- 防止多个并发接收过程
- 如果已在接收或已完成则忽略调用
- 开始递归流读取

**示例:**
```typescript
receiver.receive();
```

### `stop(): void`

停止消息接收过程并释放资源。

**行为:**
- 如果活跃则取消流读取器
- 设置内部标志以防止进一步处理
- 安全清理资源

**示例:**
```typescript
receiver.stop();
```

## 属性

### 公共属性

- `packages: IMessagePackage[]` - 成功处理的消息包数组
- `packageId: number` - 当前处理包的ID（如果没有则为-1）
- `packageType: MessagePackageType | null` - 当前包的类型
- `packageBuffer: MessageChunk[]` - 存储当前包块的缓冲区
- `chunkId: number` - 当前处理块的ID
- `reader: ReadableStreamDefaultReader | null` - 流读取器实例
- `decoder: TextDecoder | null` - UTF-8文本解码器

### 私有属性

- `isReceiving: boolean` - 指示接收是否活跃的标志
- `isDone: boolean` - 指示接收是否完成的标志

## 事件

MessageReceiver 通过 EventEmitter 接口发出以下事件：

### `MessageReceiverEvent.CHUNK_RECEIVED`
**载荷:** `MessageChunk`
**触发时机:** 当接收并解析新块时
**用途:** 监控单个块的接收

### `MessageReceiverEvent.PACKAGE_RECEIVING`
**载荷:** `IMessagePackage` (状态: Loading)
**触发时机:** 在包组装过程中用于流式显示
**用途:** 使用部分内容更新UI以实现实时显示

### `MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED`
**载荷:** `IMessagePackage` (状态: Finished)
**触发时机:** 当完整包被组装和验证时
**用途:** 处理完整的消息包

### `MessageReceiverEvent.HEADER_RECEIVED`
**载荷:** `IMessagePackage` (package_id: 0)
**触发时机:** 当接收到第一个包（头部）时
**用途:** 处理对话元数据和初始化

### `MessageReceiverEvent.MESSAGE_FINISHED`
**载荷:** `MessageChunk` (event_type: End)
**触发时机:** 当接收到结束事件块时
**用途:** 处理消息完成信号

### `MessageReceiverEvent.DONE`
**载荷:** 无
**触发时机:** 当流正常结束时
**用途:** 处理消息接收的完成

### `MessageReceiverEvent.ERROR`
**载荷:** `MessageError`
**触发时机:** 当处理过程中发生任何错误时
**用途:** 处理各种错误条件

### `MessageReceiverEvent.CANCELLED`
**载荷:** 无
**触发时机:** 当接收被取消时
**用途:** 处理取消场景

## 使用示例

### 基本用法

```typescript
import { MessageReceiver, MessageReceiverEvent } from '@/message/receiver';

// 从HTTP响应创建接收器
const response = await fetch('/api/chat/stream');
const receiver = new MessageReceiver(response.body);

// 监听完整包
receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, (pkg) => {
  console.log('接收到包:', pkg);
  // 处理完整包
});

// 监听流式更新
receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (pkg) => {
  console.log('部分包:', pkg);
  // 使用部分内容更新UI
});

// 处理错误
receiver.on(MessageReceiverEvent.ERROR, (error) => {
  console.error('接收错误:', error);
});

// 开始接收
receiver.receive();
```

### 高级事件处理

```typescript
const receiver = new MessageReceiver(stream);

// 处理对话初始化
receiver.on(MessageReceiverEvent.HEADER_RECEIVED, (headerPkg) => {
  const header = JSON.parse(headerPkg.data);
  console.log('对话ID:', header.conversation_id);
  console.log('消息ID:', header.message_id);
});

// 监控单个块
receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, (chunk) => {
  console.log(`包 ${chunk.package_id} 的块 ${chunk.chunk_id}`);
});

// 处理消息完成
receiver.on(MessageReceiverEvent.MESSAGE_FINISHED, () => {
  console.log('消息流完成');
});

// 处理流完成
receiver.on(MessageReceiverEvent.DONE, () => {
  console.log('所有包已接收');
  console.log('总包数:', receiver.packages.length);
});

receiver.receive();
```

### 与React组件集成

```typescript
import { useEffect, useState } from 'react';
import { MessageReceiver, MessageReceiverEvent } from '@/message/receiver';

function ChatComponent() {
  const [messages, setMessages] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');

  useEffect(() => {
    const receiver = new MessageReceiver(streamResponse);

    // 在流式传输期间更新当前消息
    receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (pkg) => {
      if (pkg.package_type === MessagePackageType.Text) {
        setCurrentMessage(pkg.data);
      }
    });

    // 包完成时最终确定消息
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, (pkg) => {
      if (pkg.package_type === MessagePackageType.Text) {
        setMessages(prev => [...prev, pkg.data]);
        setCurrentMessage('');
      }
    });

    receiver.receive();

    return () => receiver.stop();
  }, [streamResponse]);

  return (
    <div>
      {messages.map((msg, i) => <div key={i}>{msg}</div>)}
      {currentMessage && <div className="streaming">{currentMessage}</div>}
    </div>
  );
}
```

## 集成点

### 与HTTP客户端

MessageReceiver与支持流式响应的HTTP客户端集成：

```typescript
// 使用fetch API
const response = await fetch('/api/stream');
const receiver = new MessageReceiver(response.body);

// 使用axios（需要响应流）
const response = await axios.get('/api/stream', { responseType: 'stream' });
const receiver = new MessageReceiver(response.data);
```

### 与消息系统

接收器与其他消息系统组件协作：

- **Message类**: 包用于创建Message实例
- **PackageRender**: 在UI中渲染接收到的包
- **AgentCore**: 在应用程序中编排消息流

### 与状态管理

```typescript
// 与状态管理集成
receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, (pkg) => {
  store.dispatch(addMessagePackage(pkg));
});
```

## 错误处理

MessageReceiver处理各种错误场景：

### 错误类型

1. **网络错误**: 连接问题、流中断
2. **解析错误**: 块数据中的无效JSON
3. **验证错误**: 不完整的包、无效的块序列
4. **服务器错误**: 来自服务器的错误事件

### 错误处理模式

```typescript
receiver.on(MessageReceiverEvent.ERROR, (error: MessageError) => {
  switch (error.code) {
    case MessageErrorCode.NetworkError:
      // 处理连接问题
      console.error('网络错误:', error.message);
      break;

    case MessageErrorCode.ParsingError:
      // 处理数据解析问题
      console.error('解析错误:', error.message);
      break;

    case MessageErrorCode.IncompletePackage:
      // 处理不完整包场景
      console.error('不完整包:', error.message);
      break;

    case MessageErrorCode.InvalidChunk:
      // 处理块验证错误
      console.error('无效块:', error.message);
      break;

    case MessageErrorCode.ServerError:
      // 处理服务器端错误
      console.error('服务器错误:', error.message);
      break;
  }
});
```

## TypeScript 类型

### 核心接口

```typescript
interface MessageChunk {
  package_id: number;
  package_type: MessagePackageType;
  chunk_id: number;
  is_last: boolean;
  data: string;
  event_id: number;
  event_type: EventType;
}

interface IMessagePackage {
  package_id: number;
  package_type: MessagePackageType;
  status: MessagePackageStatus;
  data: string;
}
```

### 枚举

```typescript
enum MessagePackageType {
  Text = 0,        // 文本
  Structured = 1,  // 结构化
  Thinking = 2,    // 思维过程
  Error = 3        // 错误
}

enum MessagePackageStatus {
  Loading = 0,   // 加载中
  Finished = 1,  // 已完成
  Error = 2      // 错误
}

enum EventType {
  Start = 1000,   // 开始
  Loading = 1001, // 加载中
  End = 1002,     // 结束
  Error = 2000    // 错误
}

enum MessageReceiverEvent {
  HEADER_RECEIVED = "header_received",              // 接收到消息头
  MESSAGE_FINISHED = "message_finished",            // 消息完成
  PACKAGE_FINISHED_RECEIVED = "package_received",   // 接收到完整包
  PACKAGE_RECEIVING = "package_receiving",          // 正在接收包
  CHUNK_RECEIVED = "chunk_received",                // 接收到块
  DONE = "done",                                    // 完成
  CANCELLED = "cancelled",                          // 取消
  ERROR = "error"                                   // 错误
}
```

### 错误类型

```typescript
enum MessageErrorCode {
  IncompletePackage = "INCOMPLETE_PACKAGE", // 不完整包
  InvalidChunk = "INVALID_CHUNK",           // 无效块
  ParsingError = "PARSING_ERROR",           // 解析错误
  NetworkError = "NETWORK_ERROR",           // 网络错误
  ServerError = "SERVER_ERROR"              // 服务器错误
}

class MessageError {
  message: string;
  code: MessageErrorCode;
  error: any;
}
```

## 最佳实践

1. **始终处理错误**: 监听ERROR事件以处理各种失败场景
2. **清理资源**: 当组件卸载或不再需要接收时调用 `stop()`
3. **使用适当的事件**: 使用PACKAGE_RECEIVING进行流式UI，使用PACKAGE_FINISHED_RECEIVED进行最终处理
4. **验证包类型**: 在处理包数据之前检查package_type
5. **处理异步操作**: 记住所有事件都是异步的
6. **监控内存使用**: 大型流可能会累积许多包；考虑清理策略

## 性能考虑

- 接收器按顺序处理块并验证排序
- 包组装涉及排序和连接操作
- 事件发射在事件循环内是同步的
- 内存使用随保留的包数量增长
- 流处理针对实时显示场景进行了优化

## 测试

MessageReceiver包含全面的测试覆盖。参见 `__tests__/receiver.test.ts` 的示例：

- 基本消息接收
- 多块包处理
- 错误场景测试
- 事件发射验证
- 流生命周期管理
