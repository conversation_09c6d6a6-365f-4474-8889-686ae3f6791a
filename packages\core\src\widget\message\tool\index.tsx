import { Divider, Typography } from "antd";
import React from "react";

import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from "@ant-design/icons";
import { Icon } from "@cscs-agent/icons";

interface ToolWidgetProps {
  name: string;
  result?: string;
  status?: string;
}

const ToolWidget: React.FC<ToolWidgetProps> = (props) => {
  const { name, result, status } = props ?? {};

  return (
    <div className="ag:bg-white ag:my-2 ag:px-3 ag:py-2 ag:border ag:border-[rgba(0,0,0,0.09)] ag:rounded-md ag:w-[300px]">
      <div className="ag:flex ag:justify-between">
        <div>
          <Icon icon="Tool" style={{ color: "rgba(37, 45, 62, 0.45)" }} />
          <span className="ag:pl-3 ag:text-[rgba(37,45,62,0.65)]">调用工具：{name}</span>
        </div>
        {status === "failed" && (
          <Typography.Text type="danger">
            <CloseCircleOutlined />
          </Typography.Text>
        )}
        {(status === "success" || status === undefined) && (
          <Typography.Text type="success">
            <CheckCircleOutlined />
          </Typography.Text>
        )}
        {status === "loading" && (
          <Typography.Text type="success">
            <LoadingOutlined />
          </Typography.Text>
        )}
      </div>
      {result && (
        <>
          <Divider type="horizontal" style={{ margin: "8px 0" }} />
          <div className="ag:mt-2 ag:text-xs">{result}</div>
        </>
      )}
    </div>
  );
};

export default ToolWidget;
