# SelectInteractiveWidget

一个支持静态数据和动态接口请求的交互式选择器组件。

## 功能特性

- ✅ 支持静态选项数据传入
- ✅ 支持通过接口动态获取选项数据
- ✅ 支持多种 HTTP 方法（GET、POST、PUT、DELETE、PATCH）
- ✅ 支持自定义请求头和参数
- ✅ 支持请求取消和错误处理
- ✅ 支持加载状态显示
- ✅ 静态数据和接口数据可以同时使用

## 使用方式

### 1. 仅使用静态数据

```tsx
<SelectInteractiveWidget
  options={[
    { label: "选项1", value: "option1" },
    { label: "选项2", value: "option2" },
    { label: "选项3", value: "option3" }
  ]}
  placeholder="请选择"
  defaultValue="option1"
/>
```

### 2. 仅使用接口数据

```tsx
<SelectInteractiveWidget
  apiConfig={{
    url: "/api/options",
    method: "GET",
    headers: {
      "Authorization": "Bearer token"
    },
    params: {
      category: "type1"
    }
  }}
  placeholder="请选择"
/>
```

### 3. 同时使用静态数据和接口数据

```tsx
<SelectInteractiveWidget
  options={[
    { label: "默认选项", value: "default" }
  ]}
  apiConfig={{
    url: "/api/dynamic-options",
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: {
      filter: "active"
    }
  }}
  placeholder="请选择"
/>
```

## API 配置

### ApiConfig 接口

```typescript
interface ApiConfig {
  /** 接口地址 */
  url: string;
  /** 请求方法，默认为 GET */
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求参数（用于 GET 和 DELETE） */
  params?: Record<string, any>;
  /** 请求体数据（用于 POST、PUT、PATCH） */
  data?: Record<string, any>;
}
```

### 接口返回数据格式

组件支持多种接口返回数据格式：

#### 格式1：直接返回数组
```json
[
  { "label": "选项1", "value": "value1" },
  { "label": "选项2", "value": "value2" }
]
```

#### 格式2：包含 options 字段
```json
{
  "options": [
    { "label": "选项1", "value": "value1" },
    { "label": "选项2", "value": "value2" }
  ]
}
```

#### 格式3：包含 data 字段
```json
{
  "data": [
    { "label": "选项1", "value": "value1" },
    { "label": "选项2", "value": "value2" }
  ]
}
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| options | `Array<{label: string, value: string}>` | `[]` | 静态选项数据 |
| apiConfig | `ApiConfig` | - | 接口配置 |
| placeholder | `string` | - | 占位符文本 |
| defaultValue | `string` | - | 默认选中值 |
| disabledAfterSended | `boolean` | `true` | 发送后是否禁用 |

## 状态管理

- **加载状态**：当接口请求进行中时，组件会显示加载状态
- **错误处理**：接口请求失败时会显示错误信息
- **请求取消**：组件卸载时会自动取消进行中的请求

## 注意事项

1. 静态 `options` 和接口返回的选项会合并显示
2. 接口请求在组件挂载时自动触发
3. 当 `apiConfig` 发生变化时会重新发起请求
4. 组件会自动处理请求取消，避免内存泄漏
5. 支持项目现有的请求拦截器和错误处理机制
