import React, { useEffect, useMemo } from "react";

import { useCommandRunner, useSubscribeCommand } from "@/command";
import { useActiveConversationId } from "@/core";
import { useActiveAgentConfig } from "@/core/hooks/useAgent";
import { BuildInCommand, IRenderSidePanelParams, WidgetConfig } from "@/types";

const SidePanelRender: React.FC = () => {
  const agentConfig = useActiveAgentConfig();
  const [renderWidgetConfig, setRenderWidgetConfig] = React.useState<WidgetConfig | null>(null);
  const [widgetProps, setWidgetProps] = React.useState<Record<string, unknown> | null>(null);
  const [activeConversationId] = useActiveConversationId();
  const runner = useCommandRunner();

  const widgets = useMemo(() => {
    return agentConfig?.sidePanel?.render?.widgets ?? [];
  }, [agentConfig]);

  useSubscribeCommand(BuildInCommand.RenderSidePanel, (params) => {
    const { widgetCode: code, widgetProps: props } = params as IRenderSidePanelParams;
    const widget = widgets.find((i) => {
      return i.code === code;
    });
    if (!widget) {
      console.error("Widget not found:", code);
      return;
    }

    setRenderWidgetConfig(null);
    setWidgetProps(null);

    setTimeout(() => {
      setWidgetProps({ ...widget?.props, ...props });
      setRenderWidgetConfig(widget);
    }, 0);
  });

  // 切换会话时，清空侧边栏
  useEffect(() => {
    setRenderWidgetConfig(null);
    setWidgetProps(null);
    runner(BuildInCommand.CloseSidePanel);
  }, [activeConversationId]);

  const WidgetComponent = useMemo(() => {
    if (!renderWidgetConfig) return null;
    return renderWidgetConfig.component;
  }, [renderWidgetConfig]);

  return WidgetComponent ? <WidgetComponent {...widgetProps} /> : null;
};

export default SidePanelRender;
