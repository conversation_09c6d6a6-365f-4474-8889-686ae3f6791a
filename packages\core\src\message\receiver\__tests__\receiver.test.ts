import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import { EventType, MessageErrorCode, MessagePackageStatus, MessagePackageType } from "@/types";

import { MessageReceiver, MessageReceiverEvent } from "../receiver";
import { createMessageChunk, createMockResponse, createSSEData } from "./mocks";

describe("MessageReceiver", () => {
  // 模拟控制台错误输出
  beforeEach(() => {
    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("应该正确初始化", () => {
    // 创建一个空的响应对象
    const mockResponse = createMockResponse([]).data.body;
    const receiver = new MessageReceiver(mockResponse);

    // 验证初始状态
    expect(receiver.packages).toEqual([]);
    expect(receiver.packageId).toBe(-1);
    expect(receiver.packageType).toBeNull();
    expect(receiver.packageBuffer).toEqual([]);
    expect(receiver.chunkId).toBe(-1);
    expect(receiver.reader).not.toBeNull();
    expect(receiver.decoder).not.toBeNull();
  });

  it("应该正确处理单个消息块", async () => {
    // 创建一个包含单个消息块的响应
    const chunk = createMessageChunk(0, MessagePackageType.Text, 0, true, "Hello, World!");
    const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;

    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onChunkReceived = vi.fn();
    const onPackageReceived = vi.fn();
    const onDone = vi.fn();

    receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);
    receiver.on(MessageReceiverEvent.DONE, onDone);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发
    expect(onChunkReceived).toHaveBeenCalledWith(chunk);
    expect(onPackageReceived).toHaveBeenCalledWith({
      package_id: 0,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });
    expect(onDone).toHaveBeenCalled();

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0]).toEqual({
      package_id: 0,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });
  });

  it("应该正确处理多个有序的消息块", async () => {
    // 创建多个有序的消息块
    const chunks = [
      createMessageChunk(0, MessagePackageType.Text, 0, false, "Hello, "),
      createMessageChunk(0, MessagePackageType.Text, 1, false, "World"),
      createMessageChunk(0, MessagePackageType.Text, 2, true, "!"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onChunkReceived = vi.fn();
    const onPackageReceiving = vi.fn();
    const onPackageReceived = vi.fn();

    receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);
    receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, onPackageReceiving);
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发
    expect(onChunkReceived).toHaveBeenCalledTimes(3);
    expect(onPackageReceiving).toHaveBeenCalledTimes(3);
    expect(onPackageReceived).toHaveBeenCalledWith({
      package_id: 0,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0].data).toBe("Hello, World!");
  });

  it("应该正确处理多个无序的消息块", async () => {
    // 创建多个无序的消息块
    const chunks = [
      createMessageChunk(0, MessagePackageType.Text, 1, false, "World"),
      createMessageChunk(0, MessagePackageType.Text, 0, false, "Hello, "),
      createMessageChunk(0, MessagePackageType.Text, 2, true, "!"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onPackageReceived = vi.fn();
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发
    expect(onPackageReceived).toHaveBeenCalledWith({
      package_id: 0,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0].data).toBe("Hello, World!");
  });

  it("应该正确处理多个消息包", async () => {
    // 创建多个消息包的块
    const chunks = [
      createMessageChunk(0, MessagePackageType.Text, 0, true, "First package"),
      createMessageChunk(1, MessagePackageType.Structured, 0, false, '{"key":'),
      createMessageChunk(1, MessagePackageType.Structured, 1, true, '"value"}'),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onPackageReceived = vi.fn();
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发次数
    expect(onPackageReceived).toHaveBeenCalledTimes(2);

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(2);
    expect(receiver.packages[0].package_id).toBe(0);
    expect(receiver.packages[0].package_type).toBe(MessagePackageType.Text);
    expect(receiver.packages[0].data).toBe("First package");

    expect(receiver.packages[1].package_id).toBe(1);
    expect(receiver.packages[1].package_type).toBe(MessagePackageType.Structured);
    expect(receiver.packages[1].data).toBe('{"key":"value"}');
  });

  it("应该处理不完整的消息包", async () => {
    // 创建不完整的消息包（缺少最后一个块）
    const chunks = [
      createMessageChunk(0, MessagePackageType.Text, 0, false, "Hello, "),
      createMessageChunk(0, MessagePackageType.Text, 1, false, "World"),
      // 缺少 is_last=true 的块
      createMessageChunk(1, MessagePackageType.Text, 0, true, "New package"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onError = vi.fn();
    const onPackageReceived = vi.fn();

    receiver.on(MessageReceiverEvent.ERROR, onError);
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证错误事件触发
    expect(onError).toHaveBeenCalled();

    // 验证第二个包被正确处理
    expect(onPackageReceived).toHaveBeenCalledWith({
      package_id: 1,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: "New package",
    });

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0].package_id).toBe(1);
  });

  it("应该处理不连续的消息块", async () => {
    // 创建不连续的消息块（缺少中间的块）
    const chunks = [
      createMessageChunk(0, MessagePackageType.Text, 0, false, "Hello, "),
      // 缺少 chunk_id=1 的块
      createMessageChunk(0, MessagePackageType.Text, 2, true, "!"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onError = vi.fn();

    receiver.on(MessageReceiverEvent.ERROR, onError);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证错误事件触发
    expect(onError).toHaveBeenCalled();

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(0);
  });

  it("应该正确处理停止接收", async () => {
    // 创建一个长消息
    const chunks = [
      createMessageChunk(0, MessagePackageType.Text, 0, false, "This is "),
      createMessageChunk(0, MessagePackageType.Text, 1, false, "a long "),
      createMessageChunk(0, MessagePackageType.Text, 2, true, "message."),
    ];

    // 模拟一个延迟返回的响应
    const mockReader = {
      read: vi.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({ value: createSSEData([chunks[0]]), done: false });
          }, 50);
        });
      }),
      cancel: vi.fn().mockResolvedValue(undefined),
    };

    const mockResponse = {
      data: {
        body: {
          getReader: () => mockReader,
        },
      },
      status: 200,
      statusText: "OK",
      headers: {},
      config: {} as any,
    };

    const receiver = new MessageReceiver(mockResponse.data.body as any);

    // 监听事件
    const onChunkReceived = vi.fn();
    receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);

    // 开始接收
    receiver.receive();

    // 立即停止接收
    receiver.stop();

    // 等待一段时间
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证 cancel 被调用
    expect(mockReader.cancel).toHaveBeenCalled();
  });

  describe("事件类型处理", () => {
    it("应该正确处理 HEADER_RECEIVED 事件", async () => {
      // 创建 package_id 为 0 的消息包（头部包）
      const chunk = createMessageChunk(0, MessagePackageType.Structured, 0, true, '{"conversation_id":"123","message_id":"456"}');
      const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;

      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onHeaderReceived = vi.fn();
      const onPackageReceived = vi.fn();

      receiver.on(MessageReceiverEvent.HEADER_RECEIVED, onHeaderReceived);
      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证头部事件触发
      expect(onHeaderReceived).toHaveBeenCalledWith({
        package_id: 0,
        package_type: MessagePackageType.Structured,
        status: MessagePackageStatus.Finished,
        data: '{"conversation_id":"123","message_id":"456"}',
      });

      // 验证包接收事件也被触发
      expect(onPackageReceived).toHaveBeenCalledWith({
        package_id: 0,
        package_type: MessagePackageType.Structured,
        status: MessagePackageStatus.Finished,
        data: '{"conversation_id":"123","message_id":"456"}',
      });
    });

    it("应该正确处理 MESSAGE_FINISHED 事件", async () => {
      // 创建带有 EventType.End 的消息块
      const chunk = {
        package_id: 0,
        package_type: MessagePackageType.Text,
        chunk_id: 0,
        is_last: true,
        data: "Message completed",
        event_id: 1002,
        event_type: EventType.End,
      };

      const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onMessageFinished = vi.fn();
      const onChunkReceived = vi.fn();

      receiver.on(MessageReceiverEvent.MESSAGE_FINISHED, onMessageFinished);
      receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证消息完成事件触发
      expect(onMessageFinished).toHaveBeenCalledWith(chunk);
      expect(onChunkReceived).toHaveBeenCalledWith(chunk);
    });

    it("应该正确处理 EventType.Error 事件", async () => {
      // 创建带有 EventType.Error 的消息块
      const errorChunk = {
        package_id: 0,
        package_type: MessagePackageType.Error,
        chunk_id: 0,
        is_last: true,
        data: '{"error_message":"Something went wrong"}',
        event_id: 2000,
        event_type: EventType.Error,
      };

      const mockResponse = createMockResponse([createSSEData([errorChunk])]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onError = vi.fn();
      const onPackageReceived = vi.fn();

      receiver.on(MessageReceiverEvent.ERROR, onError);
      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证错误事件触发
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Something went wrong",
          code: MessageErrorCode.ServerError,
        }),
      );

      // 验证错误包被创建
      expect(onPackageReceived).toHaveBeenCalledWith({
        package_id: -1, // Error events use the current packageId, which starts at -1
        package_type: MessagePackageType.Error,
        status: MessagePackageStatus.Finished,
        data: '{"error_message":"Something went wrong"}',
      });
    });
  });

  describe("消息包类型处理", () => {
    it("应该正确处理 Thinking 类型的消息包", async () => {
      const chunks = [
        createMessageChunk(0, MessagePackageType.Thinking, 0, false, "Let me think about this..."),
        createMessageChunk(0, MessagePackageType.Thinking, 1, true, " The answer is 42."),
      ];

      const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onPackageReceived = vi.fn();
      const onPackageReceiving = vi.fn();

      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);
      receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, onPackageReceiving);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证 Thinking 类型包被正确处理
      expect(onPackageReceived).toHaveBeenCalledWith({
        package_id: 0,
        package_type: MessagePackageType.Thinking,
        status: MessagePackageStatus.Finished,
        data: "Let me think about this... The answer is 42.",
      });

      // 验证流式接收事件
      expect(onPackageReceiving).toHaveBeenCalledTimes(2);
    });

    it("应该正确处理 Error 类型的消息包", async () => {
      const chunk = createMessageChunk(0, MessagePackageType.Error, 0, true, "Error occurred");

      const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onPackageReceived = vi.fn();

      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证 Error 类型包被正确处理
      expect(onPackageReceived).toHaveBeenCalledWith({
        package_id: 0,
        package_type: MessagePackageType.Error,
        status: MessagePackageStatus.Finished,
        data: "Error occurred",
      });
    });
  });

  describe("错误处理和边界情况", () => {
    it("应该处理 JSON 解析错误", async () => {
      // 创建包含无效 JSON 的 SSE 数据
      const invalidSSEData = new TextEncoder().encode("data: {invalid json}\n");
      const mockResponse = createMockResponse([invalidSSEData]).data.body;

      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onError = vi.fn();
      receiver.on(MessageReceiverEvent.ERROR, onError);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证解析错误事件触发
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Failed to parse chunk data",
          code: MessageErrorCode.ParsingError,
        }),
      );
    });

    it("应该处理空数据行", async () => {
      // 创建包含空行的 SSE 数据
      const chunk = createMessageChunk(0, MessagePackageType.Text, 0, true, "Hello");
      const sseDataWithEmptyLines = new TextEncoder().encode(`\n\ndata: ${JSON.stringify(chunk)}\n\n`);
      const mockResponse = createMockResponse([sseDataWithEmptyLines]).data.body;

      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onPackageReceived = vi.fn();
      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证包仍然被正确处理
      expect(onPackageReceived).toHaveBeenCalledWith({
        package_id: 0,
        package_type: MessagePackageType.Text,
        status: MessagePackageStatus.Finished,
        data: "Hello",
      });
    });

    it("应该处理非 SSE 格式的数据行", async () => {
      // 创建包含非 SSE 格式数据的响应
      const nonSSEData = new TextEncoder().encode("some random text\nmore text\n");
      const chunk = createMessageChunk(0, MessagePackageType.Text, 0, true, "Hello");
      const validSSEData = createSSEData([chunk]);

      const mockResponse = createMockResponse([nonSSEData, validSSEData]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onPackageReceived = vi.fn();
      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证有效的 SSE 数据仍然被处理
      expect(onPackageReceived).toHaveBeenCalledWith({
        package_id: 0,
        package_type: MessagePackageType.Text,
        status: MessagePackageStatus.Finished,
        data: "Hello",
      });
    });

    it("应该处理网络错误", async () => {
      // 模拟网络错误
      const mockReader = {
        read: vi.fn().mockRejectedValue(new Error("Network connection failed")),
        cancel: vi.fn().mockResolvedValue(undefined),
      };

      const mockResponse = {
        getReader: () => mockReader,
      };

      const receiver = new MessageReceiver(mockResponse as any);

      // 监听事件
      const onError = vi.fn();
      receiver.on(MessageReceiverEvent.ERROR, onError);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证网络错误事件触发
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Network connection failed",
          code: MessageErrorCode.NetworkError,
        }),
      );
    });

    it("应该处理 EventType.Error 中的无效 JSON", async () => {
      // 创建带有无效 JSON 错误数据的消息块
      const errorChunk = {
        package_id: 0,
        package_type: MessagePackageType.Error,
        chunk_id: 0,
        is_last: true,
        data: "invalid json error data",
        event_id: 2000,
        event_type: EventType.Error,
      };

      const mockResponse = createMockResponse([createSSEData([errorChunk])]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onError = vi.fn();
      receiver.on(MessageReceiverEvent.ERROR, onError);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证错误事件触发，使用默认错误消息
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Unknown error",
          code: MessageErrorCode.ServerError,
        }),
      );
    });

    it("应该处理包类型为 null 的情况", async () => {
      // 创建一个消息块，但不设置包类型
      const chunk = createMessageChunk(0, MessagePackageType.Text, 0, true, "Hello");
      const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;

      const receiver = new MessageReceiver(mockResponse);

      // 手动清空包类型以模拟异常情况
      receiver.packageType = null;
      receiver.packageId = 0;
      receiver.packageBuffer = [chunk];

      // 监听事件
      const onError = vi.fn();
      receiver.on(MessageReceiverEvent.ERROR, onError);

      // 直接调用 processCompletePackage 来测试这种边界情况
      (receiver as any).processCompletePackage();

      // 验证错误事件触发
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "Package type is not set",
          code: MessageErrorCode.InvalidChunk,
        }),
      );
    });
  });

  describe("流控制和状态管理", () => {
    it("应该防止重复调用 receive()", async () => {
      const chunk = createMessageChunk(0, MessagePackageType.Text, 0, true, "Hello");
      const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;

      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onPackageReceived = vi.fn();
      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 多次调用 receive()
      receiver.receive();
      receiver.receive();
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证包只被处理一次
      expect(onPackageReceived).toHaveBeenCalledTimes(1);
    });

    it("应该在完成后防止再次调用 receive()", async () => {
      const chunk = createMessageChunk(0, MessagePackageType.Text, 0, true, "Hello");
      const mockResponse = createMockResponse([createSSEData([chunk])]).data.body;

      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onDone = vi.fn();
      const onPackageReceived = vi.fn();
      receiver.on(MessageReceiverEvent.DONE, onDone);
      receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

      // 第一次接收
      receiver.receive();

      // 等待完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证完成事件触发
      expect(onDone).toHaveBeenCalled();

      // 重置 mock
      onPackageReceived.mockClear();

      // 尝试再次接收
      receiver.receive();

      // 等待一段时间
      await new Promise((resolve) => setTimeout(resolve, 50));

      // 验证不会再次处理
      expect(onPackageReceived).not.toHaveBeenCalled();
    });

    it("应该正确处理 PACKAGE_RECEIVING 事件的状态", async () => {
      const chunks = [
        createMessageChunk(0, MessagePackageType.Text, 0, false, "Hello, "),
        createMessageChunk(0, MessagePackageType.Text, 1, true, "World!"),
      ];

      const mockResponse = createMockResponse([createSSEData(chunks)]).data.body;
      const receiver = new MessageReceiver(mockResponse);

      // 监听事件
      const onPackageReceiving = vi.fn();
      receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, onPackageReceiving);

      // 开始接收
      receiver.receive();

      // 等待异步操作完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 验证 PACKAGE_RECEIVING 事件被正确触发
      expect(onPackageReceiving).toHaveBeenCalledTimes(2);

      // 验证第一次调用的状态是 Loading
      expect(onPackageReceiving).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          status: MessagePackageStatus.Loading,
          data: "Hello, ",
        }),
      );

      // 验证第二次调用的状态是 Finished
      expect(onPackageReceiving).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          status: MessagePackageStatus.Finished,
          data: "Hello, World!",
        }),
      );
    });
  });
});
