import { WidgetConfig } from "@/types";

import RadioGroupInteractiveWidget from "./message/radio-group";
import SelectInteractiveWidget from "./message/select";
import ToolWidget from "./message/tool";

export const buildInWidgets: WidgetConfig[] = [
  {
    code: "@BuildIn/Tool",
    component: ToolWidget,
  },
  {
    code: "@BuildIn/RadioGroupInteractive",
    component: RadioGroupInteractiveWidget,
  },
  {
    code: "@BuildIn/SelectInteractive",
    component: SelectInteractiveWidget,
  },
];
