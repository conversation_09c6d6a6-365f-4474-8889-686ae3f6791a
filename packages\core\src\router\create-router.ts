import React from "react";
import { RouteObject, createBrowserRouter } from "react-router";

import { getLoginUrl } from "@/core/common/vars";

interface RouterConfig {
  pages?: {
    login?: {
      enable: boolean;
      path?: string;
      Component?: React.FC;
    };
    home?: {
      path?: string;
      Component?: React.FC;
    };
    chat?: {
      path?: string;
      Component?: React.FC;
    };
    agentHome?: {
      path?: string;
      Component?: React.FC;
    };
  };
  routes?: RouteObject[];
  rootRoutes?: RouteObject[];
  authGuard?: () => boolean;
  loginUrl?: string;
  basename?: string;
}

export interface CreateRouterOptions {
  basename?: string;
}

export function createDefaultRouter(config: RouterConfig) {
  const { pages = {}, routes = [], rootRoutes = [], authGuard, loginUrl, basename = "" } = config;

  const $routes: RouteObject[] = [
    ...rootRoutes,
    {
      path: "/",
      Component: pages.home?.Component,
      // 鉴权Guard
      loader: () => {
        if (config.authGuard) {
          const isAuthed = authGuard ? authGuard() : true;
          if (!isAuthed) {
            location.href = loginUrl ?? getLoginUrl();
          }
        }
      },
      children: [
        {
          path: "chat/:id",
          Component: pages.chat?.Component,
        },
        {
          path: "agent/:code",
          Component: pages.agentHome?.Component,
        },
      ],
      ...routes,
    },
  ];

  if (pages.login?.enable) {
    const loginPageRoute: RouteObject = {
      path: "/login",
      Component: pages.login?.Component,
    };
    $routes.unshift(loginPageRoute);
  }

  const router = createBrowserRouter($routes, {
    basename,
  });

  return router;
}
