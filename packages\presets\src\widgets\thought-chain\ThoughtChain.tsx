import React, { useEffect, useMemo, useState } from "react";

import { <PERSON><PERSON>hain as AntdXThought<PERSON>hain } from "@ant-design/x";
import type {
  ThoughtChainItem as AntdXThoughtChainItem,
  ThoughtChainProps as AntdXThoughtChainProps,
} from "@ant-design/x";

interface ThoughtChainProps {
  items: Array<AntdXThoughtChainItem & { id: string }>;
}

const ThoughtChain: React.FC<ThoughtChainProps> = (props) => {
  const { items = [] } = props;
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const collapsible: AntdXThoughtChainProps["collapsible"] = {
    expandedKeys,
    onExpand: (keys: string[]) => {
      setExpandedKeys(keys);
    },
  };

  const finalItems = useMemo(() => {
    return (
      items?.map((item) => ({
        key: item?.id,
        ...item,
      })) ?? []
    );
  }, [items]);

  useEffect(() => {
    if (items.length > 0) {
      const lastNode = items[items.length - 1];
      if (lastNode.status === "pending") {
        setExpandedKeys([...expandedKeys, lastNode.id]);
      }
    }
  }, [items.length]);

  return (
    <div>
      <AntdXThoughtChain items={finalItems} collapsible={collapsible} />
    </div>
  );
};

export default ThoughtChain;
