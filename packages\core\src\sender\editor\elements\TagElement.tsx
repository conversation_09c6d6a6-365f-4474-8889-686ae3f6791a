import { Tooltip } from "antd";
import React from "react";
import { RenderElementProps, useFocused, useSelected } from "slate-react";

export interface TagElementProps {
  content?: string;
  rawValue?: string;
  tooltips?: string;
}

// Tag Element Component
export const TagElement: React.FC<RenderElementProps> = ({ attributes, element }) => {
  const selected = useSelected();
  const focused = useFocused();
  const { content, tooltips } = element as TagElementProps;

  return (
    <span
      {...attributes}
      contentEditable={false}
      style={{
        display: "inline-block",
        margin: "0 4px",
        padding: "4px 8px",
        borderRadius: "6px",
        backgroundColor: "rgba(11,104,230,0.1)",
        color: "#666",
        border: selected && focused ? "2px solid #0B68E6" : "none",
        userSelect: "none",
        lineHeight: "1.2em",
      }}
    >
      <Tooltip title={tooltips}>
        <span>{content ?? ""}</span>
      </Tooltip>
    </span>
  );
};

export default TagElement;
