import { <PERSON><PERSON>, <PERSON>, Row } from "antd";
import React, { useEffect, useState } from "react";

import { SyncOutlined } from "@ant-design/icons";
import { BuildInCommand, StandardResponse, get, useActiveAgentCode, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface SuggestionConfig {
  prompt: string;
  heat: number;
}

const PopularQuestion: React.FC = () => {
  const [activeAgentCode] = useActiveAgentCode();
  const [questions, setQuestions] = React.useState<SuggestionConfig[]>([]);
  const runner = useCommandRunner();
  const [changeVisible, setChangeVisible] = useState(false);

  useEffect(() => {
    setQuestions([]);
    setChangeVisible(false);
    if (!activeAgentCode) return;
    handleRefresh();
  }, [activeAgentCode]);

  const handleRefresh = () => {
    if (!activeAgentCode) return;
    get<StandardResponse<SuggestionConfig[]>>(`/popular-question`, { agent_code: activeAgentCode }).then((res) => {
      if (Array.isArray(res.data.data)) {
        setQuestions(res.data.data);
        if (res.data.data.length === 4) {
          setChangeVisible(true);
        }
      } else {
        setQuestions([]);
      }
    });
  };

  const handleClick = (question: string) => {
    runner(BuildInCommand.SendMessage, { message: question, isNewConversation: true, agentCode: activeAgentCode });
  };

  return (
    <div className="pts:w-[800px]">
      <div className="pts:flex pts:justify-between pts:mb-3">
        <div>
          <Icon icon="Chat" className="pts:text-[#6C90F2]" />
          <span className="pts:ml-2 pts:text-black-85 pts:text-sm">猜你想问</span>
        </div>
        {changeVisible && (
          <Button
            type="text"
            size="small"
            icon={
              <span className="pts:text-black-45">
                <SyncOutlined />
              </span>
            }
            onClick={handleRefresh}
          >
            <span className="pts:text-black-45">换一换</span>
          </Button>
        )}
      </div>
      <Row gutter={[16, 16]}>
        {questions.map((question) => (
          <Col span={12} key={question.prompt}>
            <div
              onClick={() => handleClick(question.prompt)}
              className="pts:bg-white pts:px-4 pts:py-2 pts:border pts:border-[rgba(74,88,172,0.09)] pts:rounded-lg pts:text-black-65 pts:hover:cursor-pointer"
            >
              {question.prompt}
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default PopularQuestion;
