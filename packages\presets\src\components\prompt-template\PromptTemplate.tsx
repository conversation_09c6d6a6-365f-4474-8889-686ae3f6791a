import { Col, Row, Tooltip } from "antd";
import React, { useMemo } from "react";

import { CopyOutlined } from "@ant-design/icons";
import { BuildInCommand, useActiveAgentConfig, useCommandRunner } from "@cscs-agent/core";

const TemplateModal: React.FC = () => {
  const activeAgentConfig = useActiveAgentConfig();
  const runner = useCommandRunner();

  const cards = useMemo(() => {
    if (!activeAgentConfig) return [];
    return activeAgentConfig?.prompts ?? [];
  }, [activeAgentConfig]);

  const sendMessage = (message: string) => {
    runner(BuildInCommand.InsertTextIntoSender, {
      text: message,
    });
    runner(BuildInCommand.CloseSenderHeaderPanel);
  };

  return (
    <div className="pts:bg-white pts:w-full">
      <Row gutter={[16, 16]}>
        {cards.map((card, index) => (
          <Col span={6} key={index}>
            <div
              className="pts:p-3 pts:border pts:border-gray-200 pts:hover:border-blue-500 pts:rounded-sm pts:h-[84px] pts:cursor-pointer"
              onClick={() => sendMessage(card.prompt)}
            >
              <h4 className="pts:flex pts:items-start pts:mb-1 pts:font-medium pts:text-gray-800 pts:text-sm">
                <span className="pts:bg-[rgba(108,144,242,0.10)] pts:px-1 pts:rounded-sm pts:text-[#6C90F2] pts:text-sm">
                  {card.icon ?? <CopyOutlined />}
                </span>
                <span className="pts:ml-2">{card.title}</span>
              </h4>
              <p className="pts:m-0 pts:overflow-hidden pts:text-black-65 pts:text-xs pts:text-ellipsis pts:leading-relaxed pts:whitespace-nowrap">
                <Tooltip title={card.description}>{card.description}</Tooltip>
              </p>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default TemplateModal;
