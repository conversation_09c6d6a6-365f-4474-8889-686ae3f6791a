import { describe, expect, it } from "vitest";

import { BlockStatus } from "@/types";
import { render, screen } from "@testing-library/react";

import ErrorBlock from "../ErrorBlock";

describe("ErrorBlock", () => {
  it("should render error message with Alert component", () => {
    const content = "This is an error message";
    const status = BlockStatus.Error;

    render(<ErrorBlock content={content} status={status} />);

    // Check if the Alert component is rendered
    expect(screen.getByRole("alert")).toBeTruthy();

    // Check if the error message is displayed
    expect(screen.getByText(content)).toBeTruthy();
  });

  it("should have correct CSS classes", () => {
    const content = "Error message";
    const status = BlockStatus.Error;

    const { container } = render(<ErrorBlock content={content} status={status} />);

    // Check if the container has the expected class
    expect(container.firstChild?.className).toContain("ag:my-2");
  });
});
