/**
 * Request error handling module
 *
 * 实现请求错误处理功能
 */

import { message as antdMessage } from "antd";
import axios, { AxiosError } from "axios";

import { getLoginUrl } from "@/core/common/vars";
import { StandardErrorResponse } from "@/types";

import { DefaultErrorHandlerOption as DefaultErrorHandlerOptions } from "./types";

/**
 * 自定义错误类
 * 扩展Error类，添加额外的错误信息
 */
export class RequestError extends Error {
  /** HTTP状态码 */
  public status?: number;
  /** 错误代码 */
  public code?: string;
  /** 原始错误对象 */
  public originalError?: AxiosError;
  /** 请求URL */
  public url?: string;
  /** 请求方法 */
  public method?: string;

  /**
   * 构造函数
   * @param message 错误消息
   * @param status HTTP状态码
   * @param code 错误代码
   * @param originalError 原始错误对象
   */
  constructor(
    message: string,
    status?: number,
    code?: string,
    originalError?: AxiosError,
    url?: string,
    method?: string,
  ) {
    super(message);
    this.name = "RequestError";
    this.status = status;
    this.code = code;
    this.originalError = originalError;
    this.url = url;
    this.method = method;
  }
}

let defaultErrorHandlerOption: DefaultErrorHandlerOptions = {
  redirectToLogin: true,
};

export const setDefaultErrorHandlerOptions = (option: DefaultErrorHandlerOptions) => {
  defaultErrorHandlerOption = {
    ...defaultErrorHandlerOption,
    ...option,
  };
};

/**
 * 默认错误处理函数
 * @param error Axios错误对象
 * @returns 处理后的错误对象
 */
export const defaultErrorHandler = (error: AxiosError<StandardErrorResponse>): Promise<never> => {
  if (axios.isCancel(error)) {
    // TODO: 请求被取消的错误
    // return Promise.reject(
    //   new RequestError(
    //     error.message || 'Request cancelled',
    //     undefined,
    //     'REQUEST_CANCELLED',
    //     error,
    //     error.config?.url,
    //     error.config?.method,
    //   )
    // );
  }

  // 获取错误信息
  const status = error.response?.status;
  const url = error.config?.url;
  const method = error.config?.method;
  const errorMessage = error.response?.data.message;

  // 根据状态码生成错误消息
  let message = error.message;
  let code = "UNKNOWN_ERROR";

  if (error.code === "ECONNABORTED") {
    message = "Request timeout";
    code = "REQUEST_TIMEOUT";
  } else if (!error.response) {
    message = "Network error";
    code = "NETWORK_ERROR";
  } else {
    // 根据HTTP状态码分类错误
    switch (status) {
      case 400:
        message = "Bad request";
        code = "BAD_REQUEST";
        break;
      case 401:
        message = "Unauthorized";
        code = "UNAUTHORIZED";
        if (defaultErrorHandlerOption.redirectToLogin) {
          location.href = defaultErrorHandlerOption.loginUrl ?? getLoginUrl();
        }
        break;
      case 403:
        message = "Forbidden";
        code = "FORBIDDEN";
        break;
      case 404:
        message = "Not found";
        code = "NOT_FOUND";
        antdMessage.error(errorMessage || "Not found");
        break;
      case 500:
        message = "Internal server error";
        code = "SERVER_ERROR";
        antdMessage.error("Internal server error");
        break;
      default:
        message = `Request failed with status ${status}`;
        code = "REQUEST_FAILED";
    }
  }

  // 尝试从响应中获取更详细的错误信息
  // TODO: 类型
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const responseData = error.response?.data as any;
  if (responseData && typeof responseData === "object") {
    if (responseData.message) {
      message = responseData.message;
    }
    if (responseData.code) {
      code = responseData.code;
    }
  }

  return Promise.reject(new RequestError(message, status, code, error, url, method));
};

/**
 * 检查是否为RequestError
 * @param error 错误对象
 * @returns 是否为RequestError
 */
export const isRequestError = (error: RequestError): error is RequestError => {
  return error instanceof RequestError;
};
