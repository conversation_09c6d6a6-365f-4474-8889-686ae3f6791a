# AgentConfig 架构设计与使用文档

## 目录

1. [架构概览](#架构概览)
2. [AgentConfig 类型分析](#agentconfig-类型分析)
3. [使用模式](#使用模式)
4. [配置指南](#配置指南)
5. [集成点](#集成点)
6. [代码示例](#代码示例)
7. [组件系统](#组件系统)
8. [最佳实践](#最佳实践)

## 架构概览

CSCS Agent 系统基于模块化、配置驱动的架构构建，能够创建具有可定制 UI 组件、行为和集成的智能对话智能体。`AgentConfig` 接口作为中央配置契约，定义了智能体在系统中的结构和行为方式。

### 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    AgentChatHost                            │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                AgentChatContext                         ││
│  │  ┌─────────────────────────────────────────────────────┐││
│  │  │                AgentStore                           │││
│  │  │  • messages: Message[]                              │││
│  │  │  • activeAgentCode: string                          │││
│  │  │  • conversations: IConversation[]                   │││
│  │  │  • isLoadingMessage: boolean                        │││
│  │  └─────────────────────────────────────────────────────┘││
│  │                                                         ││
│  │  AgentConfig[] ──────────────────────────────────────── ││
│  │  • 智能体定义                                            ││
│  │  • 组件配置                                              ││
│  │  • API 端点                                              ││
│  │  • UI 插槽配置                                           ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 核心模块

- **@cscs-agent/core**: 核心框架，包含类型、状态管理和基础组件
- **@cscs-agent/agents**: 预构建的智能体配置和专用组件
- **@cscs-agent/presets**: 可复用的 UI 组件和布局
- **@cscs-agent/icons**: 用于一致 UI 元素的图标库

## AgentConfig 类型分析

### 核心接口结构

```typescript
export interface AgentConfig {
  // 基本信息
  name: string;                    // 智能体显示名称
  code: string;                    // 唯一标识符（字母数字、-、_）
  avatar?: string;                 // 智能体头像 URL
  logo?: string;                   // 智能体 Logo URL
  welcome?: string;                // 欢迎消息
  popularQuestion?: boolean;       // 启用热门问题
  description?: string;            // 智能体描述

  // UI 配置
  message?: MessageConfig;         // 消息区域配置
  sender?: SenderConfig;           // 输入区域配置
  sidePanel?: SidePanelConfig;     // 侧边面板配置

  // 功能配置
  prompts?: PromptConfig[];        // 可用的提示词模板
  commands?: CommandConfig[];      // 可执行命令
  suggestions?: SuggestionConfig[]; // 快速建议

  // API 配置
  request: {
    chat: RequestConfig;           // 聊天 API 配置
    global?: RequestConfig;        // 全局 API 配置
  };
}
```

### 组件配置系统

组件系统基于 `WidgetConfig` 接口：

```typescript
export interface WidgetConfig {
  code: string;                    // 唯一组件标识符
  description?: string;            // 组件描述
  component: React.FC<any>;        // React 组件
  props?: Record<string, any>;     // 默认属性
}

export interface MessageSlotWidgetConfig extends WidgetConfig {
  role?: Role;                     // 组件角色（AI/Human）
}
```

### 配置区域

#### 1. 消息区域配置
- **blocks.widgets**: 在消息内容中显示的组件
- **slots.header**: 基于角色渲染的头部插槽组件
- **slots.footer**: 底部插槽组件（复制、评分等）

#### 2. 发送器区域配置
- **slots.headerPanel**: 可展开的头部面板组件
- **slots.header**: 头部组件（输入工具）
- **slots.footer**: 底部组件
- **headerPanel**: 面板配置（高度、标题等）

#### 3. 侧边面板配置
- **render.widgets**: 主要侧边面板内容组件
- **slots.header/footer**: 侧边面板插槽组件

## 使用模式

### 1. 智能体注册与发现

智能体通过 `AgentChatConfig` 接口注册：

```typescript
export const config: AgentChatConfig = {
  agents: [
    dynamicPageAgentConfig,
    qaAgentConfig,
    customAgentConfig
  ],
  request?: RequestConfig  // 全局请求配置
};
```

### 2. 智能体选择与激活

系统使用 React Context 和状态管理来处理智能体生命周期：

```typescript
// 在 AgentList 组件中选择智能体
const handleAgentClick = (code: string) => {
  navigate(`/agent/${code}`);
  setActiveAgentMenuCode(code);
  setActiveAgentCode(code);
  setMessages([]);
};

// 获取活跃智能体
const useActiveAgentConfig = () => {
  const agents = useAgentConfigs();
  const [activeAgentCode] = useActiveAgentCode();

  return agents.find(i => i.code === activeAgentCode);
};
```

### 3. 组件渲染管道

组件通过发现和实例化过程进行渲染：

```typescript
// 在 WidgetBlock 中发现组件
const widgets = useMemo(() => {
  const $widgets = agentConfig?.message?.blocks?.widgets ?? [];
  return [...buildInWidgets, ...$widgets];
}, [agentConfig]);

// 组件实例化
const WidgetRender: React.FC<WidgetRenderProps> = ({ config }) => {
  const Component = config.component;
  return <Component {...config.props} />;
};
```

## 配置指南

### 1. 智能体代码命名约定
- 使用描述性的 kebab-case 名称：`dynamic-page-creator`、`qa-assistant`
- 确保所有智能体间的唯一性
- 仅使用字母数字字符、连字符和下划线

### 2. 组件代码命名约定
- 使用命名空间代码：`@DynamicPage/PreviewButton`、`@BuildIn/Tool`
- 命名空间格式：`@{模块}/{组件名称}`
- 内置组件使用 `@BuildIn/` 前缀

### 3. API 配置
- 始终提供聊天端点配置
- 对内部 API 使用相对 URL
- 包含认证所需的头信息
- 考虑请求/响应超时配置

### 4. 组件属性管理
- 在组件配置中定义默认属性
- 支持通过 XML 属性进行运行时属性覆盖
- 验证属性类型并提供回退值

## 集成点

### 1. 状态管理集成

AgentConfig 通过以下方式与全局状态集成：
- **AgentStore**: 管理活跃智能体、消息和对话
- **AgentChatContext**: 提供配置和存储访问
- **React Hooks**: `useActiveAgentConfig`、`useAgentConfigs`

### 2. 命令系统集成

智能体可以定义与全局命令系统集成的自定义命令：

```typescript
commands: [
  {
    name: "customAction",
    action: (params) => {
      // 自定义命令逻辑
      runner(BuildInCommand.OpenSidePanel, { width: 500 });
    }
  }
]
```

### 3. 消息系统集成

AgentConfig 通过以下方式影响消息渲染：
- 组件块解析和渲染
- 基于角色的插槽组件过滤
- 消息包处理

### 4. API 集成

请求配置启用：
- 聊天完成端点
- 自定义 API 集成
- 认证头管理
- 请求/响应转换

## 代码示例

### 1. 基础智能体配置

```typescript
export const basicAgentConfig: AgentConfig = {
  name: "基础助手",
  code: "basic-assistant",
  logo: "/assets/basic-logo.png",
  welcome: "您好！我能为您做些什么？",
  description: "一个简单的对话助手",

  message: {
    slots: {
      footer: {
        widgets: [
          { code: "Copy", component: Copy, role: Role.AI },
          { code: "Rating", component: Rating, role: Role.AI }
        ]
      }
    }
  },

  request: {
    chat: {
      url: "/api/chat",
      method: "POST",
      headers: { "Content-Type": "application/json" }
    }
  }
};
```

### 2. 带自定义组件的高级智能体

```typescript
export const advancedAgentConfig: AgentConfig = {
  name: "高级助手",
  code: "advanced-assistant",

  message: {
    blocks: {
      widgets: [
        { code: "@Custom/DataVisualization", component: DataVizWidget }
      ]
    }
  },

  sender: {
    slots: {
      headerPanel: {
        widgets: [
          { code: "PromptTemplate", component: PromptTemplate }
        ]
      }
    },
    headerPanel: {
      enable: true,
      height: 200,
      title: "高级工具"
    }
  },

  sidePanel: {
    render: {
      widgets: [
        {
          code: "@Custom/Analytics",
          component: AnalyticsWidget,
          props: { apiEndpoint: "/api/analytics" }
        }
      ]
    }
  }
};
```

### 3. 动态配置的工厂模式

```typescript
export function createAgentConfig(params: {
  apiUrl: string;
  features: string[];
}): AgentConfig {
  const baseConfig: AgentConfig = {
    name: "动态智能体",
    code: "dynamic-agent",
    request: {
      chat: { url: params.apiUrl }
    }
  };

  // 根据功能特性有条件地添加组件
  if (params.features.includes('preview')) {
    baseConfig.message = {
      blocks: {
        widgets: [
          { code: "@Preview/Button", component: PreviewButton }
        ]
      }
    };
  }

  return baseConfig;
}
```

## 组件系统

### 内置组件

核心系统提供了几个内置组件：

```typescript
export const buildInWidgets: WidgetConfig[] = [
  {
    code: "@BuildIn/Tool",
    component: ToolWidget,
  },
  {
    code: "@BuildIn/RadioGroupInteractive",
    component: RadioGroupInteractiveWidget,
  },
  {
    code: "@BuildIn/SelectInteractive",
    component: SelectInteractiveWidget,
  },
];
```

### 组件发现与渲染

组件通过合并过程被发现：

1. **内置组件** 始终可用
2. **智能体特定组件** 从活跃智能体配置中合并
3. **组件解析** 在消息解析期间发生

```typescript
// 组件发现过程
const widgets = useMemo(() => {
  const $widgets = agentConfig?.message?.blocks?.widgets ?? [];
  return [...buildInWidgets, ...$widgets];
}, [agentConfig]);

// 从 XML 实例化组件
const parsedWidget = parser.parse(xmlStr).widget;
const widget = widgets.find(w => w.code === parsedWidget.code);
```

### 自定义组件开发

#### 1. 组件结构

```typescript
interface CustomWidgetProps {
  // 定义组件属性
  title?: string;
  data?: any[];
  onAction?: (params: any) => void;
}

const CustomWidget: React.FC<CustomWidgetProps> = (props) => {
  const { title, data, onAction } = props;
  const runner = useCommandRunner();

  const handleClick = () => {
    // 触发系统命令
    runner(BuildInCommand.OpenSidePanel, { width: 400 });

    // 调用自定义动作
    onAction?.(data);
  };

  return (
    <div>
      <h3>{title}</h3>
      <button onClick={handleClick}>操作</button>
    </div>
  );
};
```

#### 2. 组件注册

```typescript
// 在智能体配置中
export const agentConfig: AgentConfig = {
  // ... 其他配置
  message: {
    blocks: {
      widgets: [
        {
          code: "@Custom/MyWidget",
          component: CustomWidget,
          props: {
            title: "默认标题",
            data: []
          }
        }
      ]
    }
  }
};
```

#### 3. 在消息中使用组件

组件可以使用 XML 语法嵌入到 AI 响应中：

```xml
<widget code="@Custom/MyWidget">
  <props title="动态标题" data='[{"id": 1, "name": "项目 1"}]' />
</widget>
```

### 基于插槽的组件系统

#### 消息插槽
- **头部插槽**: 在消息顶部渲染
- **底部插槽**: 在消息底部渲染（复制、评分）
- **角色过滤**: 组件可以是角色特定的（AI/Human）

#### 发送器插槽
- **头部面板**: 带工具的可展开面板
- **头部**: 始终可见的头部工具
- **底部**: 底部区域工具

#### 侧边面板插槽
- **主渲染区域**: 主要侧边面板内容
- **头部/底部插槽**: 额外的插槽区域

### 组件通信

组件通过命令系统进行通信：

```typescript
// 在组件中
const runner = useCommandRunner();

// 发送消息
runner(BuildInCommand.SendMessage, {
  message: "你好",
  agentCode: activeAgentCode
});

// 打开侧边面板
runner(BuildInCommand.OpenSidePanel, { width: 500 });

// 向发送器插入内容
runner(BuildInCommand.InsertTextIntoSender, {
  text: "插入的文本"
});
```

## 最佳实践

### 1. 配置组织

#### 模块化配置
```typescript
// 将关注点分离到模块中
import { prompts } from './prompts';
import { suggestions } from './suggestions';
import { widgets } from './widgets';

export const agentConfig: AgentConfig = {
  name: "模块化智能体",
  code: "modular-agent",
  prompts,
  suggestions,
  message: {
    blocks: { widgets }
  },
  // ... 其余配置
};
```

#### 配置工厂
```typescript
// 使用工厂进行动态配置
export function createDynamicPageAgent(params: {
  saveApiUrl: string;
  previewUrl: string;
}): AgentConfig {
  const config = { ...baseConfig };

  // 基于参数进行自定义
  const previewWidget = config.sidePanel?.render.widgets?.find(
    w => w.code === "@DynamicPage/Preview"
  );

  if (previewWidget) {
    previewWidget.props = { ...previewWidget.props, ...params };
  }

  return config;
}
```

### 2. 组件开发

#### 组件隔离
- 保持组件自包含和可复用
- 使用属性进行配置，而非全局状态
- 实现适当的错误边界

#### 性能优化
- 对昂贵的组件使用 React.memo
- 在 hooks 中实现适当的依赖数组
- 避免不必要的重新渲染

#### 可访问性
- 提供适当的 ARIA 标签
- 确保键盘导航
- 支持屏幕阅读器

### 3. API 配置

#### 错误处理


#### 请求转换
- 使用拦截器进行请求/响应转换
- 实现适当的认证流程
- 优雅地处理网络错误

### 4. 状态管理

#### 智能体状态隔离
- 保持智能体特定状态分离
- 在智能体切换时使用适当的清理
- 实现适当的加载状态

#### 消息状态管理
- 正确处理消息流
- 实现适当的错误状态
- 高效管理对话历史

### 6. 性能考虑

#### 懒加载
- 为大型组件实现代码分割
- 对可选组件使用动态导入
- 优化包大小

#### 内存管理
- 在组件中清理事件监听器
- 实现适当的组件卸载
- 避免长时间对话中的内存泄漏

#### 渲染优化
- 对长消息列表使用虚拟滚动
- 实现适当的记忆化
- 优化重新渲染周期

## 总结

AgentConfig 架构为构建智能对话智能体提供了灵活、可扩展的基础。通过遵循本文档中概述的模式和最佳实践，开发者可以创建强大、可维护的智能体配置，与 CSCS Agent 系统无缝集成。

模块化设计允许：
- **快速原型设计** 新的智能体类型
- **可复用组件** 跨不同智能体使用
- **灵活的 UI 定制** 通过组件系统
- **可扩展架构** 用于复杂应用

有关更多示例和高级使用模式，请参考 `packages/agents` 目录中的现有智能体实现。

## 附加资源

### 相关文档
- [组件开发指南](./widget-development.md)
- [命令系统文档](./command-system.md)
- [API 集成指南](./api-integration.md)

### 示例实现
- **动态页面智能体**: `packages/agents/src/dynamic-page/config.tsx`
- **问答智能体**: `packages/agents/src/qa/config.tsx`
- **演练场示例**: `playground/src/agent-config.tsx`

### 类型定义
- **核心类型**: `packages/core/src/types.ts`
- **智能体接口**: `packages/core/src/core/hooks/useAgent.ts`
- **组件接口**: `packages/core/src/widget/index.ts`
