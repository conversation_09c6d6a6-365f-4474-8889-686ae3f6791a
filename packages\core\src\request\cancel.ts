/**
 * Request cancellation module
 *
 * 实现请求取消功能
 */

import { RequestCanceler } from "./types";

/**
 * 请求取消管理器
 * 管理所有活跃的请求，支持取消单个或所有请求
 */
export class RequestCancelManager {
  /** 存储所有活跃的请求 */
  private pendingRequests: Map<string, AbortController> = new Map();

  /**
   * 添加请求到管理器
   * @param requestId 请求标识符
   * @returns 请求取消器和AbortSignal
   */
  public addRequest(requestId: string): { canceler: RequestCanceler; signal: AbortSignal } {
    // 如果已存在相同ID的请求，先取消它
    this.removeRequest(requestId);

    // 创建新的AbortController
    const controller = new AbortController();

    // 存储请求
    this.pendingRequests.set(requestId, controller);

    // 返回取消器和信号
    return {
      canceler: {
        cancel: (message?: string) => this.cancelRequest(requestId, message),
      },
      signal: controller.signal,
    };
  }

  /**
   * 移除请求
   * @param requestId 请求标识符
   */
  public removeRequest(requestId: string): void {
    if (this.pendingRequests.has(requestId)) {
      this.pendingRequests.delete(requestId);
    }
  }

  /**
   * 取消指定请求
   * @param requestId 请求标识符
   * @param message 取消消息
   */
  public cancelRequest(requestId: string, message?: string): void {
    const controller = this.pendingRequests.get(requestId);
    if (controller) {
      controller.abort(message || `Request ${requestId} cancelled`);
      this.removeRequest(requestId);
    }
  }

  /**
   * 取消所有请求
   * @param message 取消消息
   */
  public cancelAllRequests(message?: string): void {
    this.pendingRequests.forEach((controller, requestId) => {
      controller.abort(message || `Request ${requestId} cancelled`);
    });
    this.pendingRequests.clear();
  }

  /**
   * 获取活跃请求数量
   * @returns 活跃请求数量
   */
  public getPendingRequestsCount(): number {
    return this.pendingRequests.size;
  }
}
