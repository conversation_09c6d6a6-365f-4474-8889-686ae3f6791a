import { <PERSON><PERSON>, Divider, message } from "antd";
import React, { useMemo } from "react";

import { CloseOutlined, SaveOutlined } from "@ant-design/icons";
import { BuildInCommand, post, useCommandRunner } from "@cscs-agent/core";
import { PresetsCommand } from "@cscs-agent/presets";

interface PreviewProps {
  id: string;
  name: string;
  saveApiUrl: string | (() => string);
  previewUrl: string | ((id: string) => string);
}

interface StandardResponse {
  success: boolean;
  errorMessage?: string;
  data?: any;
}

const Preview: React.FC<PreviewProps> = (props) => {
  const { id, name, saveApiUrl, previewUrl } = props ?? {};
  const runner = useCommandRunner();

  const $previewUrl = useMemo(() => {
    if (typeof previewUrl === "function") {
      return previewUrl(id);
    }
    return previewUrl;
  }, [previewUrl, id]);

  const save = () => {
    const url = typeof saveApiUrl === "function" ? saveApiUrl() : saveApiUrl;

    post<StandardResponse>(url, { dynamicId: id })
      .then((res) => {
        if (!res.data.success) {
          message.error(res.data.errorMessage);
        } else {
          message.success("保存成功");
        }
      })
      .catch((error) => {
        message.error(error.message);
      });
  };

  const close = () => {
    runner(BuildInCommand.CloseSidePanel);
    runner(PresetsCommand.OpenSideBar);
  };

  return (
    <div className="ats:flex ats:flex-col ats:w-full ats:h-full">
      <div className="ats:top-0 ats:flex ats:justify-between ats:items-center ats:bg-white ats:px-6 ats:py-3 ats:border-gray-100 ats:border-b">
        <span className="ats:text-black-85">{name}</span>
        <div>
          <Button variant="text" onClick={save} color="primary" icon={<SaveOutlined />} size="small">
            保存
          </Button>
          <Divider type="vertical" />
          <Button
            variant="text"
            onClick={close}
            color="default"
            icon={<CloseOutlined style={{ color: "rgba(37,45,62,0.45)" }} />}
            size="small"
          />
        </div>
      </div>
      <div className="ats:flex-1">
        <iframe src={$previewUrl} width={500} className="ats:w-full ats:h-full" />
      </div>
    </div>
  );
};

export default Preview;
