import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";

// interface SenderHeaderProps {}

const SenderFooter: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  const widgets = useMemo(() => {
    return agentConfig?.sender?.slots?.footer?.widgets ?? [];
  }, [agentConfig]);

  return (
    <div className="ag:pt-2 ag:flex ag:flex-wrap ag:flex-row-reverse">
      {widgets.map((Widget, index) => (
        <div className="ag:mr-2" key={index}>
          <Widget.component {...Widget.props} />
        </div>
      ))}
    </div>
  );
};

export default SenderFooter;
