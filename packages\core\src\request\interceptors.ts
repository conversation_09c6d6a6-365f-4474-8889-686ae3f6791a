/**
 * Request interceptors module
 *
 * 实现请求和响应拦截器
 */

import { AxiosHeaders, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from "axios";

import { getToken } from "@/core/common/token";

import { DefaultRequestInterceptorOptions, RequestInterceptor, ResponseInterceptor } from "./types";

// 默认请求拦截器配置
let defaultRequestInterceptorOptions: DefaultRequestInterceptorOptions = {
  addTimestamp: true,
  addToken: true,
  addPrefix: true,
  prefix: "/api",
};

export const setDefaultRequestInterceptorOptions = (option: Partial<DefaultRequestInterceptorOptions>) => {
  defaultRequestInterceptorOptions = {
    ...defaultRequestInterceptorOptions,
    ...option,
  };
};

/**
 * 默认请求拦截器
 * 添加时间戳和通用头信息
 */
export const defaultRequestInterceptor: RequestInterceptor = {
  name: "defaultRequestInterceptor",
  onFulfilled: (config: InternalAxiosRequestConfig) => {
    // 添加时间戳，防止缓存
    if (config.method?.toLowerCase() === "get") {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    // 添加 prefix 'api'
    if (defaultRequestInterceptorOptions.addPrefix) {
      // 如果url以http开头，不添加前缀
      const url = config.url?.trim();
      if (url && !url?.startsWith("http")) {
        config.url = `${defaultRequestInterceptorOptions.prefix}${config.url}`;
      }
    }

    // 添加 Token
    const token = getToken();
    config.headers = new AxiosHeaders({
      ...config.headers,
      Authorization: `Bearer ${token}`,
    });

    return config;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};

/**
 * 默认响应拦截器
 * 直接返回响应数据
 */
export const defaultResponseInterceptor: ResponseInterceptor = {
  name: "defaultResponseInterceptor",
  onFulfilled: (response: AxiosResponse) => {
    // 直接返回响应数据
    return response;
  },
  onRejected: (error) => {
    return Promise.reject(error);
  },
};

/**
 * 应用请求拦截器
 * @param instance Axios实例
 * @param interceptors 请求拦截器数组
 */
export const applyRequestInterceptors = (instance: AxiosInstance, interceptors: RequestInterceptor[] = []): void => {
  // 先添加默认拦截器
  const allInterceptors = [defaultRequestInterceptor, ...interceptors];

  // 按顺序添加拦截器
  allInterceptors.forEach((interceptor) => {
    instance.interceptors.request.use(interceptor.onFulfilled, interceptor.onRejected);
  });
};

/**
 * 应用响应拦截器
 * @param instance Axios实例
 * @param interceptors 响应拦截器数组
 */
export const applyResponseInterceptors = (instance: AxiosInstance, interceptors: ResponseInterceptor[] = []): void => {
  // 先添加自定义拦截器，最后添加默认拦截器
  // 这样默认拦截器会最后执行，确保返回数据格式一致
  const allInterceptors = [...interceptors, defaultResponseInterceptor];

  // 按顺序添加拦截器
  allInterceptors.forEach((interceptor) => {
    instance.interceptors.response.use(interceptor.onFulfilled, interceptor.onRejected);
  });
};
