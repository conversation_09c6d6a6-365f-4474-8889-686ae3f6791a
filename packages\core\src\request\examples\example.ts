/**
 * Request module usage examples
 *
 * 演示如何使用请求模块
 */

import { Request, get, post } from "../index";

/**
 * 基本用法示例
 */
export const basicUsageExample = async () => {
  try {
    // 使用默认实例的便捷方法
    const data1 = await get("https://api.example.com/users");
    console.log("GET result:", data1);

    // 使用默认实例的便捷方法，带参数
    const data2 = await get("https://api.example.com/users", { page: 1, limit: 10 });
    console.log("GET with params result:", data2);

    // 使用默认实例的便捷方法，发送POST请求
    const data3 = await post("https://api.example.com/users", { name: "<PERSON>", age: 30 });
    console.log("POST result:", data3);
  } catch (error) {
    console.error("Request error:", error);
  }
};

/**
 * 创建自定义实例示例
 */
export const customInstanceExample = async () => {
  // 创建自定义请求实例
  const customRequest = new Request({
    baseURL: "https://api.example.com",
    timeout: 5000,
    headers: {
      Authorization: "Bearer token123",
    },
  });

  try {
    // 使用自定义实例发送请求
    const data = await customRequest.get("/users");
    console.log("Custom instance result:", data);
  } catch (error) {
    console.error("Custom instance error:", error);
  }
};

/**
 * 请求取消示例
 */
export const cancelRequestExample = async () => {
  // 创建自定义请求实例
  const customRequest = new Request();

  try {
    // 发送请求，使用自定义requestId
    const requestPromise = customRequest.get("https://api.example.com/users", undefined, {
      requestId: "user-list-request",
    });

    // 取消请求
    setTimeout(() => {
      customRequest.cancelRequest("user-list-request", "User cancelled the request");
    }, 100);

    // 等待请求完成或被取消
    const data = await requestPromise;
    console.log("This should not be executed if request is cancelled", data);
  } catch (error) {
    console.error("Request was cancelled:", error);
  }
};

/**
 * 拦截器示例
 */
export const interceptorsExample = async () => {
  // 创建自定义请求实例，带拦截器
  const customRequest = new Request({
    requestInterceptors: [
      {
        name: "authInterceptor",
        onFulfilled: (config) => {
          // 添加认证头
          config.headers = config.headers || {};
          config.headers["Authorization"] = "Bearer dynamic-token";
          return config;
        },
      },
    ],
    responseInterceptors: [
      {
        name: "dataTransformInterceptor",
        onFulfilled: (response) => {
          // 转换响应数据
          if (response.data) {
            response.data = {
              ...response.data,
              timestamp: new Date().toISOString(),
            };
          }
          return response;
        },
      },
    ],
  });

  try {
    // 使用带拦截器的实例发送请求
    const data = await customRequest.get("https://api.example.com/users");
    console.log("Intercepted result:", data);
  } catch (error) {
    console.error("Intercepted error:", error);
  }
};

/**
 * 错误处理示例
 */
export const errorHandlingExample = async () => {
  // 创建自定义请求实例，带自定义错误处理
  const customRequest = new Request({
    errorHandler: (error) => {
      // 自定义错误处理逻辑
      console.error("Custom error handler:", error);

      // 可以返回自定义数据或继续抛出错误
      return Promise.reject({
        message: "Custom error message",
        originalError: error,
      });
    },
  });

  try {
    const data = await customRequest.get("https://non-existent-api.example.com");
    console.log("This should not be executed", data);
  } catch (error) {
    console.error("Custom error handling result:", error);
  }
};
