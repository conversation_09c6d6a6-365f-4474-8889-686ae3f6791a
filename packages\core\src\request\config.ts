/**
 * Request module configuration
 *
 * 定义请求模块的默认配置
 */

import { RequestConfig } from "./types";

/**
 * 默认请求配置
 */
export const defaultRequestConfig: RequestConfig = {
  // 默认超时时间: 60秒
  timeout: 60000,
  // 默认请求头
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
};

/**
 * 创建请求配置
 * @param config 用户自定义配置
 * @returns 合并后的配置
 */
export const createRequestConfig = (config?: Partial<RequestConfig>): RequestConfig => {
  return {
    ...defaultRequestConfig,
    ...config,
    headers: {
      ...defaultRequestConfig.headers,
      ...(config?.headers || {}),
    },
  };
};
