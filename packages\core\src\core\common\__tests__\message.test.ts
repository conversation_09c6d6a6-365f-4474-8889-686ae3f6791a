import { describe, expect, it } from "vitest";

import { MessagePackageStatus, MessagePackageType, MessageStatus, Role } from "@/types";

import {
  Message,
  MessageConfig,
  createAIMessage,
  createHumanMessage,
  createMessage,
  createMessageFromConfig,
} from "../message";

describe("Message Class", () => {
  const mockMessagePackage = {
    package_id: 1,
    package_type: MessagePackageType.Text,
    status: MessagePackageStatus.Finished,
    data: "Hello, World!",
  };

  describe("Constructor", () => {
    it("should create a message with valid config", () => {
      const config: MessageConfig = {
        id: "msg-123",
        role: Role.HUMAN,
        agentCode: "test-agent",
        content: [mockMessagePackage],
        status: MessageStatus.Finished,
        isFresh: true,
      };

      const message = new Message(config);

      expect(message.id).toBe("msg-123");
      expect(message.role).toBe(Role.HUMAN);
      expect(message.agentCode).toBe("test-agent");
      expect(message.content).toEqual([mockMessagePackage]);
      expect(message.status).toBe(MessageStatus.Finished);
      expect(message.isFresh).toBe(true);
    });

    it("should use default values for optional properties", () => {
      const config: MessageConfig = {
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
      };

      const message = new Message(config);

      expect(message.content).toEqual([]);
      expect(message.status).toBe(MessageStatus.Loading);
      expect(message.isFresh).toBe(false);
      expect(message.user_rating).toBeUndefined();
    });

    it("should throw error for empty id", () => {
      const config: MessageConfig = {
        id: "",
        role: Role.HUMAN,
        agentCode: "test-agent",
      };

      expect(() => new Message(config)).toThrow("Message id is required and cannot be empty");
    });

    it("should throw error for empty agentCode", () => {
      const config: MessageConfig = {
        id: "msg-123",
        role: Role.HUMAN,
        agentCode: "",
      };

      expect(() => new Message(config)).toThrow("Message agentCode is required and cannot be empty");
    });

    it("should trim id and agentCode", () => {
      const config: MessageConfig = {
        id: "  msg-123  ",
        role: Role.HUMAN,
        agentCode: "  test-agent  ",
      };

      const message = new Message(config);

      expect(message.id).toBe("msg-123");
      expect(message.agentCode).toBe("test-agent");
    });
  });

  describe("Content Management", () => {
    let message: Message;

    beforeEach(() => {
      message = new Message({
        id: "msg-123",
        role: Role.HUMAN,
        agentCode: "test-agent",
      });
    });

    it("should add content", () => {
      message.addContent(mockMessagePackage);
      expect(message.content).toHaveLength(1);
      expect(message.content[0]).toEqual(mockMessagePackage);
    });

    it("should remove content by package id", () => {
      message.addContent(mockMessagePackage);
      const result = message.removeContent(1);

      expect(result).toBe(true);
      expect(message.content).toHaveLength(0);
    });

    it("should return false when removing non-existent content", () => {
      const result = message.removeContent(999);
      expect(result).toBe(false);
    });

    it("should get content by package id", () => {
      message.addContent(mockMessagePackage);
      const content = message.getContent(1);

      expect(content).toEqual(mockMessagePackage);
    });

    it("should return undefined for non-existent content", () => {
      const content = message.getContent(999);
      expect(content).toBeUndefined();
    });
  });

  describe("Status Checks", () => {
    it("should check if message is loading", () => {
      const message = new Message({
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
        status: MessageStatus.Loading,
      });

      expect(message.isLoading()).toBe(true);
      expect(message.isFinished()).toBe(false);
      expect(message.isError()).toBe(false);
    });

    it("should check if message is finished", () => {
      const message = new Message({
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
        status: MessageStatus.Finished,
      });

      expect(message.isLoading()).toBe(false);
      expect(message.isFinished()).toBe(true);
      expect(message.isError()).toBe(false);
    });

    it("should check if message has error", () => {
      const message = new Message({
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
        status: MessageStatus.Error,
      });

      expect(message.isLoading()).toBe(false);
      expect(message.isFinished()).toBe(false);
      expect(message.isError()).toBe(true);
    });
  });

  describe("Role Checks", () => {
    it("should check if message is from human", () => {
      const message = new Message({
        id: "msg-123",
        role: Role.HUMAN,
        agentCode: "test-agent",
      });

      expect(message.isHuman()).toBe(true);
      expect(message.isAI()).toBe(false);
    });

    it("should check if message is from AI", () => {
      const message = new Message({
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
      });

      expect(message.isHuman()).toBe(false);
      expect(message.isAI()).toBe(true);
    });
  });

  describe("Text Content", () => {
    it("should get text content from message packages", () => {
      const textPackage1 = {
        package_id: 1,
        package_type: MessagePackageType.Text,
        status: MessagePackageStatus.Finished,
        data: "Hello, ",
      };

      const textPackage2 = {
        package_id: 2,
        package_type: MessagePackageType.Text,
        status: MessagePackageStatus.Finished,
        data: "World!",
      };

      const message = new Message({
        id: "msg-123",
        role: Role.HUMAN,
        agentCode: "test-agent",
        content: [textPackage1, textPackage2],
      });

      expect(message.getTextContent()).toBe("Hello, World!");
    });

    it("should filter out non-text packages", () => {
      const textPackage = {
        package_id: 1,
        package_type: MessagePackageType.Text,
        status: MessagePackageStatus.Finished,
        data: "Hello",
      };

      const structuredPackage = {
        package_id: 2,
        package_type: MessagePackageType.Structured,
        status: MessagePackageStatus.Finished,
        data: '{"key": "value"}',
      };

      const message = new Message({
        id: "msg-123",
        role: Role.HUMAN,
        agentCode: "test-agent",
        content: [textPackage, structuredPackage],
      });

      expect(message.getTextContent()).toBe("Hello");
    });
  });

  describe("Utility Methods", () => {
    let message: Message;

    beforeEach(() => {
      message = new Message({
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
      });
    });

    it("should mark and unmark as fresh", () => {
      expect(message.isFresh).toBe(false);

      message.markAsFresh();
      expect(message.isFresh).toBe(true);

      message.unmarkAsFresh();
      expect(message.isFresh).toBe(false);
    });

    it("should set rating", () => {
      expect(message.user_rating).toBeUndefined();

      message.setRating("like");
      expect(message.user_rating).toBe("like");

      message.setRating("dislike");
      expect(message.user_rating).toBe("dislike");

      message.setRating(undefined);
      expect(message.user_rating).toBeUndefined();
    });

    it("should update status", () => {
      expect(message.status).toBe(MessageStatus.Loading);

      message.updateStatus(MessageStatus.Finished);
      expect(message.status).toBe(MessageStatus.Finished);
    });
  });
});

describe("Factory Functions", () => {
  const mockMessagePackage = {
    package_id: 1,
    package_type: MessagePackageType.Text,
    status: MessagePackageStatus.Finished,
    data: "Hello, World!",
  };

  describe("createMessage", () => {
    it("should create message with all parameters", () => {
      const message = createMessage(
        "msg-123",
        Role.HUMAN,
        [mockMessagePackage],
        MessageStatus.Finished,
        "test-agent",
        "like",
      );

      expect(message.id).toBe("msg-123");
      expect(message.role).toBe(Role.HUMAN);
      expect(message.content).toEqual([mockMessagePackage]);
      expect(message.status).toBe(MessageStatus.Finished);
      expect(message.agentCode).toBe("test-agent");
      expect(message.user_rating).toBe("like");
    });
  });

  describe("createMessageFromConfig", () => {
    it("should create message from config object", () => {
      const config: MessageConfig = {
        id: "msg-123",
        role: Role.AI,
        agentCode: "test-agent",
        content: [mockMessagePackage],
      };

      const message = createMessageFromConfig(config);

      expect(message.id).toBe("msg-123");
      expect(message.role).toBe(Role.AI);
      expect(message.agentCode).toBe("test-agent");
      expect(message.content).toEqual([mockMessagePackage]);
    });
  });

  describe("createHumanMessage", () => {
    it("should create human message with correct defaults", () => {
      const message = createHumanMessage("msg-123", [mockMessagePackage], "test-agent");

      expect(message.id).toBe("msg-123");
      expect(message.role).toBe(Role.HUMAN);
      expect(message.content).toEqual([mockMessagePackage]);
      expect(message.status).toBe(MessageStatus.Finished);
      expect(message.agentCode).toBe("test-agent");
      expect(message.isFresh).toBe(false);
    });
  });

  describe("createAIMessage", () => {
    it("should create AI message with correct defaults", () => {
      const message = createAIMessage("msg-123", "test-agent");

      expect(message.id).toBe("msg-123");
      expect(message.role).toBe(Role.AI);
      expect(message.content).toEqual([]);
      expect(message.status).toBe(MessageStatus.Loading);
      expect(message.agentCode).toBe("test-agent");
      expect(message.isFresh).toBe(true);
    });

    it("should create AI message with provided content", () => {
      const message = createAIMessage("msg-123", "test-agent", [mockMessagePackage]);

      expect(message.content).toEqual([mockMessagePackage]);
    });
  });
});
