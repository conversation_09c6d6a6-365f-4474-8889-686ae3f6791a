/// <reference types="vite-plugin-svgr/client" />

import React, { CSSProperties, useMemo } from "react";

import ArrowUp from "./ArrowUp.svg?react";
import Chat from "./Chat.svg?react";
import Copy from "./Copy.svg?react";
import Maximize from "./Maximize.svg?react";
import Minimize from "./Minimize.svg?react";
import Save from "./Save.svg?react";
import Search from "./Search.svg?react";
import Send from "./Send.svg?react";
import SideBar from "./SideBar.svg?react";
import Stop from "./Stop.svg?react";
import Tool from "./Tool.svg?react";
import Web from "./Web.svg?react";

const ICON_MAP: Record<string, React.FC> = {
  Web,
  Search,
  Save,
  Chat,
  ArrowUp,
  Tool,
  Stop,
  Minimize,
  Maximize,
  Copy,
  Send,
  SideBar,
};

export const Icon: React.FC<{
  icon: string;
  style?: CSSProperties;
  className?: string;
}> = (props) => {
  const { icon } = props ?? {};

  const Svg = useMemo(() => {
    if (!icon) return null;
    return ICON_MAP[icon];
  }, [icon]);

  return Svg ? (
    <span className={`cscs-agent-icon ${props.className ?? ""}`} style={props.style}>
      <Svg />
    </span>
  ) : null;
};
