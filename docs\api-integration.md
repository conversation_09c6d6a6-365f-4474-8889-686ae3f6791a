# API 集成指南

## 目录

1. [概述](#概述)
2. [请求配置](#请求配置)
3. [HTTP 客户端](#http-客户端)
4. [聊天 API 集成](#聊天-api-集成)
5. [自定义 API 集成](#自定义-api-集成)
6. [错误处理](#错误处理)
7. [认证与安全](#认证与安全)
8. [性能优化](#性能优化)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

## 概述

CSCS Agent 系统提供了完整的 API 集成解决方案，支持 RESTful API、实时通信和自定义协议。系统基于 Axios 构建，提供了请求拦截、错误处理、超时控制和取消机制等功能。

### 核心特性

- **统一的 HTTP 客户端**: 基于 Axios 的封装，支持所有 HTTP 方法
- **请求/响应拦截**: 支持全局和局部拦截器
- **错误处理**: 统一的错误处理机制
- **请求取消**: 支持 AbortController 取消请求
- **类型安全**: 完整的 TypeScript 支持
- **配置灵活**: 支持全局和智能体级别的配置

### 支持的功能

1. **聊天 API**: 与 AI 模型的对话接口
2. **数据 API**: 获取和提交业务数据
3. **文件上传**: 支持文件上传和下载
4. **实时通信**: WebSocket 和 SSE 支持
5. **认证集成**: 支持多种认证方式

## 请求配置

### 基础配置接口

```typescript
interface RequestConfig {
  /** API端点URL */
  url?: string;
  /** HTTP方法（GET, POST等） */
  method?: string;
  /** HTTP头信息，键值对形式 */
  headers?: Record<string, string>;
  /** 请求体数据 */
  body?: any;
  /** 请求参数 */
  params?: Record<string, any>;
  /** 超时时间（毫秒） */
  timeout?: number;
}

interface RequestOptions {
  /** 请求信号，用于取消请求 */
  signal?: AbortSignal;
  /** 请求头 */
  headers?: Record<string, string>;
  /** URL 参数 */
  params?: Record<string, any>;
  /** 请求体数据 */
  data?: any;
  /** 超时时间 */
  timeout?: number;
  /** HTTP 方法 */
  method?: string;
  /** 请求 URL */
  url?: string;
}
```

### 智能体级别配置

```typescript
export const agentConfig: AgentConfig = {
  name: "API 集成智能体",
  code: "api-agent",
  
  request: {
    // 聊天 API 配置
    chat: {
      url: "/api/v1/chat/completions",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-api-key"
      },
      timeout: 30000
    },
    
    // 全局 API 配置
    global: {
      headers: {
        "X-Client-Version": "1.0.0",
        "X-Request-ID": () => generateRequestId()
      },
      timeout: 10000
    }
  }
};
```

### 全局配置

```typescript
// 在应用配置中设置全局请求配置
export const appConfig: AgentChatConfig = {
  agents: [/* 智能体配置 */],
  
  // 全局请求配置
  request: {
    headers: {
      "X-App-Name": "CSCS-Agent",
      "X-App-Version": "1.0.0"
    },
    timeout: 15000
  }
};
```

## HTTP 客户端

### 基础用法

```typescript
import { get, post, put, patch, del } from "@cscs-agent/core";

const ApiExampleWidget: React.FC = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);

  // GET 请求
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await get<DataType[]>("/api/data", {
        page: 1,
        limit: 10
      });
      setData(response.data);
    } catch (error) {
      console.error("获取数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // POST 请求
  const createData = async (newData: any) => {
    try {
      const response = await post<DataType>("/api/data", newData);
      console.log("创建成功:", response.data);
      fetchData(); // 刷新数据
    } catch (error) {
      console.error("创建失败:", error);
    }
  };

  // PUT 请求
  const updateData = async (id: string, updatedData: any) => {
    try {
      const response = await put<DataType>(`/api/data/${id}`, updatedData);
      console.log("更新成功:", response.data);
      fetchData(); // 刷新数据
    } catch (error) {
      console.error("更新失败:", error);
    }
  };

  // DELETE 请求
  const deleteData = async (id: string) => {
    try {
      await del(`/api/data/${id}`);
      console.log("删除成功");
      fetchData(); // 刷新数据
    } catch (error) {
      console.error("删除失败:", error);
    }
  };

  return (
    <div>
      <Button onClick={fetchData} loading={loading}>
        获取数据
      </Button>
      {/* 其他操作按钮 */}
    </div>
  );
};
```

### 请求取消

```typescript
const CancellableRequestWidget: React.FC = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchDataWithCancel = async () => {
    try {
      setLoading(true);
      
      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 创建新的 AbortController
      const controller = new AbortController();
      abortControllerRef.current = controller;

      const response = await get<DataType[]>("/api/slow-data", {}, {
        signal: controller.signal,
        timeout: 5000
      });

      setData(response.data);
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log("请求已取消");
      } else {
        console.error("请求失败:", error);
      }
    } finally {
      setLoading(false);
    }
  };

  const cancelRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  // 组件卸载时取消请求
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <div>
      <Button onClick={fetchDataWithCancel} loading={loading}>
        获取数据
      </Button>
      <Button onClick={cancelRequest} disabled={!loading}>
        取消请求
      </Button>
    </div>
  );
};
```

### 自定义请求配置

```typescript
const CustomRequestWidget: React.FC = () => {
  const customRequest = async () => {
    try {
      // 使用自定义配置
      const response = await get("/api/custom", {}, {
        headers: {
          "X-Custom-Header": "custom-value",
          "Accept": "application/vnd.api+json"
        },
        timeout: 20000,
        params: {
          include: "related",
          fields: "name,description"
        }
      });

      console.log("自定义请求成功:", response.data);
    } catch (error) {
      console.error("自定义请求失败:", error);
    }
  };

  return (
    <Button onClick={customRequest}>
      发送自定义请求
    </Button>
  );
};
```

## 聊天 API 集成

### 基础聊天配置

```typescript
export const chatAgentConfig: AgentConfig = {
  name: "聊天智能体",
  code: "chat-agent",
  
  request: {
    chat: {
      url: "/api/v1/chat/completions",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-your-api-key"
      }
    }
  }
};
```

### 流式响应处理

```typescript
// 系统内部的流式响应处理
class Transmitter {
  constructor(url?: string, options?: TransmitterOptions) {
    this.url = url || "/chat-api/chat";
    this.requestId = nanoid();
    
    if (options) {
      this.headers = options.headers || {};
      this.method = options.method || "POST";
      this.timeout = options.timeout || 0;
    }
  }

  async send(messages: Message[], onMessage?: (chunk: MessageChunk) => void) {
    try {
      const response = await fetch(this.url, {
        method: this.method,
        headers: {
          "Content-Type": "application/json",
          ...this.headers
        },
        body: JSON.stringify({
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          stream: true,
          request_id: this.requestId
        }),
        signal: this.abortController.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("无法读取响应流");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.trim() && line.startsWith("data: ")) {
            try {
              const data = line.slice(6);
              if (data === "[DONE]") {
                return;
              }
              
              const chunk: MessageChunk = JSON.parse(data);
              onMessage?.(chunk);
            } catch (error) {
              console.error("解析流数据失败:", error);
            }
          }
        }
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log("请求已取消");
      } else {
        throw error;
      }
    }
  }

  cancel() {
    this.abortController.abort();
  }
}
```

## 自定义 API 集成

### 组件中的 API 调用

```typescript
const DataManagementWidget: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await get<{ users: User[] }>("/api/users", {
        page: 1,
        limit: 20,
        sort: "created_at"
      });

      setUsers(response.data.users);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 创建用户
  const createUser = async (userData: Partial<User>) => {
    try {
      const response = await post<User>("/api/users", userData);
      setUsers(prev => [...prev, response.data]);
      message.success("用户创建成功");
    } catch (err) {
      message.error(`创建失败: ${err.message}`);
    }
  };

  // 更新用户
  const updateUser = async (id: string, userData: Partial<User>) => {
    try {
      const response = await put<User>(`/api/users/${id}`, userData);
      setUsers(prev => prev.map(user =>
        user.id === id ? response.data : user
      ));
      message.success("用户更新成功");
    } catch (err) {
      message.error(`更新失败: ${err.message}`);
    }
  };

  // 删除用户
  const deleteUser = async (id: string) => {
    try {
      await del(`/api/users/${id}`);
      setUsers(prev => prev.filter(user => user.id !== id));
      message.success("用户删除成功");
    } catch (err) {
      message.error(`删除失败: ${err.message}`);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  if (loading) return <Spin size="large" />;
  if (error) return <Alert message="错误" description={error} type="error" />;

  return (
    <div>
      <Button onClick={fetchUsers} style={{ marginBottom: 16 }}>
        刷新用户列表
      </Button>

      <List
        dataSource={users}
        renderItem={(user) => (
          <List.Item
            actions={[
              <Button onClick={() => updateUser(user.id, { status: "active" })}>
                激活
              </Button>,
              <Button danger onClick={() => deleteUser(user.id)}>
                删除
              </Button>
            ]}
          >
            <List.Item.Meta
              title={user.name}
              description={user.email}
            />
          </List.Item>
        )}
      />
    </div>
  );
};
```

### 文件上传

```typescript
const FileUploadWidget: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("category", "documents");

    try {
      setUploading(true);

      const response = await post<{ url: string; id: string }>(
        "/api/upload",
        formData,
        {
          headers: {
            // 不设置 Content-Type，让浏览器自动设置
          },
          timeout: 60000 // 文件上传可能需要更长时间
        }
      );

      message.success("文件上传成功");
      return response.data;
    } catch (error) {
      message.error(`上传失败: ${error.message}`);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const handleUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      const result = await uploadFile(file);
      onSuccess(result);
    } catch (error) {
      onError(error);
    }
  };

  return (
    <Upload
      customRequest={handleUpload}
      fileList={fileList}
      onChange={({ fileList }) => setFileList(fileList)}
      multiple
    >
      <Button icon={<UploadOutlined />} loading={uploading}>
        上传文件
      </Button>
    </Upload>
  );
};
```

## 认证与安全

### JWT 认证

```typescript
// 认证管理
class AuthManager {
  private token: string | null = null;

  setToken(token: string) {
    this.token = token;
    localStorage.setItem("auth_token", token);
  }

  getToken(): string | null {
    if (!this.token) {
      this.token = localStorage.getItem("auth_token");
    }
    return this.token;
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem("auth_token");
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }
}

const authManager = new AuthManager();

// 在请求中添加认证头
const authenticatedRequest = async (url: string, options: RequestOptions = {}) => {
  const token = authManager.getToken();

  if (token) {
    options.headers = {
      ...options.headers,
      "Authorization": `Bearer ${token}`
    };
  }

  try {
    return await get(url, options.params, options);
  } catch (error) {
    if (error.status === 401) {
      // Token 过期，清除并重定向到登录
      authManager.clearToken();
      window.location.href = "/login";
    }
    throw error;
  }
};
```

### API 密钥管理

```typescript
// 安全的 API 密钥配置
const getApiConfig = () => {
  const apiKey = process.env.REACT_APP_API_KEY;
  const baseUrl = process.env.REACT_APP_API_BASE_URL;

  if (!apiKey || !baseUrl) {
    throw new Error("API 配置缺失");
  }

  return {
    baseUrl,
    headers: {
      "Authorization": `Bearer ${apiKey}`,
      "X-API-Version": "v1"
    }
  };
};

// 在智能体配置中使用
export const secureAgentConfig: AgentConfig = {
  name: "安全智能体",
  code: "secure-agent",

  request: {
    chat: {
      url: `${getApiConfig().baseUrl}/chat`,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...getApiConfig().headers
      }
    }
  }
};
```

## 最佳实践

### 1. 请求优化

```typescript
// 请求去重
const requestCache = new Map<string, Promise<any>>();

const cachedRequest = async <T>(key: string, requestFn: () => Promise<T>): Promise<T> => {
  if (requestCache.has(key)) {
    return requestCache.get(key);
  }

  const promise = requestFn().finally(() => {
    requestCache.delete(key);
  });

  requestCache.set(key, promise);
  return promise;
};

// 使用示例
const OptimizedWidget: React.FC = () => {
  const fetchData = () => {
    return cachedRequest("user-data", () =>
      get<User[]>("/api/users")
    );
  };

  return <Button onClick={fetchData}>获取数据</Button>;
};
```

### 2. 错误边界

```typescript
const ApiErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={({ error }) => (
        <Alert
          message="API 调用失败"
          description={error.message}
          type="error"
          showIcon
        />
      )}
    >
      {children}
    </ErrorBoundary>
  );
};
```

### 3. 类型安全

```typescript
// 定义 API 响应类型
interface ApiResponse<T> {
  data: T;
  message: string;
  status: "success" | "error";
  timestamp: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  created_at: string;
}

// 类型安全的 API 调用
const typedApiCall = async (): Promise<User[]> => {
  const response = await get<ApiResponse<User[]>>("/api/users");

  if (response.data.status === "error") {
    throw new Error(response.data.message);
  }

  return response.data.data;
};
```

## 常见问题

### Q1: 如何处理 CORS 问题？

**A:** 在开发环境中配置代理：
```json
// package.json
{
  "proxy": "http://localhost:8080"
}
```

或使用 setupProxy.js：
```javascript
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8080',
      changeOrigin: true,
    })
  );
};
```

### Q2: 如何处理大文件上传？

**A:** 使用分片上传：
```typescript
const uploadLargeFile = async (file: File) => {
  const chunkSize = 1024 * 1024; // 1MB
  const chunks = Math.ceil(file.size / chunkSize);

  for (let i = 0; i < chunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);

    const formData = new FormData();
    formData.append("chunk", chunk);
    formData.append("chunkIndex", i.toString());
    formData.append("totalChunks", chunks.toString());
    formData.append("fileName", file.name);

    await post("/api/upload-chunk", formData);
  }

  // 合并文件
  await post("/api/merge-chunks", { fileName: file.name });
};
```

### Q3: 如何实现请求重试？

**A:** 参考前面的重试机制实现，或使用指数退避算法：
```typescript
const exponentialBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries = 3
): Promise<T> => {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      return await fn();
    } catch (error) {
      retries++;
      if (retries === maxRetries) throw error;

      const delay = Math.pow(2, retries) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw new Error("Max retries exceeded");
};
```
