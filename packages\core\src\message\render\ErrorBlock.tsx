import { Alert } from "antd";
import React, { useMemo } from "react";

import { BlockStatus, ErrorMessage } from "@/types";

interface ErrorBlockProps {
  content: string;
  status: BlockStatus;
}

const ErrorBlock: React.FC<ErrorBlockProps> = (props) => {
  const { content } = props;

  const message = useMemo(() => {
    try {
      const error = JSON.parse(content) as ErrorMessage;
      return error.error_message;
    } catch {
      return content;
    }
  }, [content]);

  return (
    <div className="ag:my-2">
      <Alert message={message} type="error" showIcon />
    </div>
  );
};

export default ErrorBlock;
