# 命令系统文档

## 目录

1. [概述](#概述)
2. [命令系统架构](#命令系统架构)
3. [内置命令](#内置命令)
4. [使用命令](#使用命令)
5. [自定义命令](#自定义命令)
6. [命令订阅](#命令订阅)
7. [命令作用域](#命令作用域)
8. [最佳实践](#最佳实践)
9. [常见问题](#常见问题)

## 概述

CSCS Agent 系统的命令系统是一个基于事件驱动的通信机制，允许组件之间、组件与系统之间进行解耦的通信。通过命令系统，组件可以触发系统级操作、与其他组件交互，而无需直接依赖。

### 核心特性

- **解耦通信**: 组件间通过命令进行通信，无需直接引用
- **事件驱动**: 基于发布-订阅模式的异步事件系统
- **作用域隔离**: 命令在智能体作用域内执行，避免冲突
- **类型安全**: 完整的 TypeScript 支持
- **可扩展性**: 支持自定义命令和处理器

### 使用场景

1. **组件间通信**: 组件之间传递数据和状态
2. **系统操作**: 触发系统级功能（发送消息、打开面板等）
3. **状态管理**: 更新全局状态和智能体状态
4. **用户交互**: 响应用户操作并执行相应逻辑

## 命令系统架构

### 核心组件

```typescript
// 命令发射器 - 基于 EventEmitter3
export const commandEmitter: CommandEmitter = new Emitter();

// 命令名称生成
function createCommandName(name: string, scope: string): string {
  return `@command/${scope}:${name}`;
}
```

### 命令流程

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   组件 A    │───▶│  命令发射器  │───▶│   组件 B    │
│ (发送命令)   │    │ (事件分发)   │    │ (接收命令)   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
  useCommandRunner    commandEmitter    useSubscribeCommand
```

### 类型定义

```typescript
// 命令回调函数类型
export type CommandCallback = (params: any) => any;

// 命令取消订阅函数类型
export type CommandUnsubscribe = () => void;

// 命令发射器接口
export interface CommandEmitter extends Emitter {}

// 基础命令参数接口
export interface IBaseCommandParams {
  [key: string]: any;
}

// 注册的命令接口
export interface RegisteredCommand {
  name: string;
  description?: string;
  category?: string;
  handler: CommandCallback;
}
```

## 内置命令

系统提供了一系列内置命令来执行常见操作：

### 面板控制命令

```typescript
export enum BuildInCommand {
  // 侧边面板控制
  OpenSidePanel = "openSidePanel",        // 打开侧边面板
  CloseSidePanel = "closeSidePanel",      // 关闭侧边面板
  RenderSidePanel = "renderSidePanel",    // 渲染侧边面板内容
  
  // 发送器面板控制
  CloseSenderHeaderPanel = "closeSenderHeaderPanel", // 关闭发送器头部面板
}
```

#### 使用示例

```typescript
const PanelControlWidget: React.FC = () => {
  const runner = useCommandRunner();

  const openSidePanel = () => {
    // 打开侧边面板
    runner(BuildInCommand.OpenSidePanel, { 
      width: 500 
    });
  };

  const renderAndOpenSidePanel = () => {
    // 渲染特定组件到侧边面板
    runner(BuildInCommand.RenderSidePanel, {
      widgetCode: "@Custom/DataVisualization",
      widgetProps: {
        data: [1, 2, 3, 4, 5],
        chartType: "line"
      }
    });

    // 然后打开侧边面板
    runner(BuildInCommand.OpenSidePanel, { 
      width: 600 
    });
  };

  return (
    <div>
      <Button onClick={openSidePanel}>打开侧边面板</Button>
      <Button onClick={renderAndOpenSidePanel}>显示图表</Button>
    </div>
  );
};
```

### 消息控制命令

```typescript
export enum BuildInCommand {
  // 消息操作
  SendMessage = "sendMessage",                    // 发送消息
  ResendPreviousMessage = "resendPreviousMessage", // 重新发送上一条消息
  CancelChatRequest = "cancelChatRequest",        // 取消聊天请求
  
  // 会话管理
  NewConversationCreated = "newConversationCreated", // 新建会话
}
```

#### 使用示例

```typescript
const MessageControlWidget: React.FC = () => {
  const runner = useCommandRunner();

  const sendMessage = () => {
    runner(BuildInCommand.SendMessage, {
      message: "这是一条来自组件的消息",
      agentCode: "current-agent",
      conversationId: "conversation-123"
    });
  };

  const resendMessage = () => {
    runner(BuildInCommand.ResendPreviousMessage);
  };

  const cancelRequest = () => {
    runner(BuildInCommand.CancelChatRequest);
  };

  return (
    <div>
      <Button onClick={sendMessage}>发送消息</Button>
      <Button onClick={resendMessage}>重发消息</Button>
      <Button onClick={cancelRequest}>取消请求</Button>
    </div>
  );
};
```

### 输入控制命令

```typescript
export enum BuildInCommand {
  // 发送器内容插入
  InsertTextIntoSender = "insertTextIntoSender",           // 插入文本
  InsertTagIntoSender = "insertTagIntoSender",             // 插入标签
  InsertEditableTagIntoSender = "insertEditableTagIntoSender", // 插入可编辑标签
  InsertSelectIntoSender = "insertSelectIntoSender",       // 插入选择器
}
```

#### 使用示例

```typescript
const InputControlWidget: React.FC = () => {
  const runner = useCommandRunner();

  const insertText = () => {
    runner(BuildInCommand.InsertTextIntoSender, {
      text: "插入的文本内容"
    });
  };

  const insertTag = () => {
    runner(BuildInCommand.InsertTagIntoSender, {
      text: "企业名称",
      rawValue: "企业名称(id: 123456)",
      tooltips: "点击查看企业详情"
    });
  };

  const insertSelect = () => {
    runner(BuildInCommand.InsertSelectIntoSender, {
      placeholder: "请选择企业类型",
      options: [
        { label: "有限责任公司", value: "limited_company" },
        { label: "股份有限公司", value: "joint_stock_company" },
        { label: "个人独资企业", value: "sole_proprietorship" }
      ],
      defaultValue: "limited_company",
      tooltips: "选择企业的法律组织形式"
    });
  };

  return (
    <div>
      <Button onClick={insertText}>插入文本</Button>
      <Button onClick={insertTag}>插入标签</Button>
      <Button onClick={insertSelect}>插入选择器</Button>
    </div>
  );
};
```

## 使用命令

### 1. 命令执行器 (useCommandRunner)

```typescript
import { useCommandRunner } from "@cscs-agent/core";

const MyComponent: React.FC = () => {
  const runner = useCommandRunner();

  const handleAction = () => {
    // 执行内置命令
    runner(BuildInCommand.OpenSidePanel, { width: 400 });
    
    // 执行自定义命令
    runner("customCommand", { data: "some data" });
  };

  return <Button onClick={handleAction}>执行命令</Button>;
};
```

### 2. 命令参数

不同命令接受不同的参数：

```typescript
// 发送消息命令参数
interface SendMessageParams {
  message: string;
  agentCode: string;
  conversationId?: string;
  isNewConversation?: boolean;
}

// 打开侧边面板命令参数
interface OpenSidePanelParams {
  width?: number;
  widgetCode?: string;
}

// 渲染侧边面板命令参数
interface RenderSidePanelParams {
  widgetCode: string;
  widgetProps?: Record<string, any>;
}

// 插入文本命令参数
interface InsertTextParams {
  text: string;
}

// 插入标签命令参数
interface InsertTagParams {
  text: string;
  rawValue?: string;
  tooltips?: string;
}

// 插入选择器命令参数
interface InsertSelectParams {
  placeholder?: string;
  options: Array<{ label: string; value: string }>;
  defaultValue?: string;
  tooltips?: string;
  disabled?: boolean;
}
```

### 3. 异步命令处理

```typescript
const AsyncCommandWidget: React.FC = () => {
  const runner = useCommandRunner();
  const [loading, setLoading] = useState(false);

  const handleAsyncAction = async () => {
    try {
      setLoading(true);
      
      // 执行一系列命令
      runner(BuildInCommand.RenderSidePanel, {
        widgetCode: "@Custom/LoadingWidget",
        widgetProps: { message: "正在处理..." }
      });
      
      runner(BuildInCommand.OpenSidePanel, { width: 400 });
      
      // 模拟异步操作
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 更新侧边面板内容
      runner(BuildInCommand.RenderSidePanel, {
        widgetCode: "@Custom/ResultWidget",
        widgetProps: { result: "处理完成" }
      });
      
    } catch (error) {
      console.error("命令执行失败:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button onClick={handleAsyncAction} loading={loading}>
      执行异步操作
    </Button>
  );
};
```

## 自定义命令

### 1. 在智能体配置中定义

```typescript
import { AgentConfig } from "@cscs-agent/core";

export const myAgentConfig: AgentConfig = {
  name: "自定义智能体",
  code: "custom-agent",

  // 定义自定义命令
  commands: [
    {
      name: "refreshData",
      description: "刷新数据",
      action: (params) => {
        console.log("刷新数据:", params);

        // 可以执行任何逻辑
        fetchDataFromAPI(params.apiUrl)
          .then(data => {
            // 更新组件状态或触发其他命令
            runner(BuildInCommand.RenderSidePanel, {
              widgetCode: "@Custom/DataDisplay",
              widgetProps: { data }
            });
          });
      }
    },
    {
      name: "exportData",
      description: "导出数据",
      action: (data) => {
        const blob = new Blob([JSON.stringify(data, null, 2)], {
          type: "application/json"
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `data-${Date.now()}.json`;
        link.click();
        URL.revokeObjectURL(url);
      }
    },
    {
      name: "showNotification",
      description: "显示通知",
      action: ({ message, type = "info" }) => {
        // 使用 Ant Design 的 message 组件
        import("antd").then(({ message: antdMessage }) => {
          antdMessage[type](message);
        });
      }
    }
  ],

  // 其他配置...
  request: {
    chat: { url: "/api/chat" }
  }
};
```

### 2. 使用自定义命令

```typescript
const CustomCommandWidget: React.FC = () => {
  const runner = useCommandRunner();

  const refreshData = () => {
    runner("refreshData", {
      apiUrl: "/api/latest-data",
      timestamp: Date.now()
    });
  };

  const exportCurrentData = () => {
    const data = {
      items: [1, 2, 3],
      timestamp: Date.now(),
      version: "1.0"
    };
    runner("exportData", data);
  };

  const showSuccess = () => {
    runner("showNotification", {
      message: "操作成功完成！",
      type: "success"
    });
  };

  return (
    <div>
      <Button onClick={refreshData}>刷新数据</Button>
      <Button onClick={exportCurrentData}>导出数据</Button>
      <Button onClick={showSuccess}>显示成功消息</Button>
    </div>
  );
};
```

## 最佳实践

### 1. 命令命名规范

```typescript
// ✅ 好的命名
const goodCommands = [
  "refreshData",           // 动词 + 名词
  "exportUserData",        // 具体明确
  "showErrorMessage",      // 描述性强
  "updateUserProfile",     // 清晰的动作
  "validateFormData"       // 明确的功能
];

// ❌ 避免的命名
const badCommands = [
  "data",                  // 不明确
  "doSomething",          // 太泛化
  "action1",              // 无意义
  "handleClick",          // 太技术化
  "process"               // 不具体
];
```

### 2. 错误处理

```typescript
const SafeCommandWidget: React.FC = () => {
  const runner = useCommandRunner();

  const safeExecuteCommand = async (commandName: string, params: any) => {
    try {
      runner(commandName, params);
    } catch (error) {
      console.error(`命令执行失败: ${commandName}`, error);

      // 显示用户友好的错误信息
      runner("showNotification", {
        message: "操作失败，请稍后重试",
        type: "error"
      });
    }
  };

  return (
    <Button onClick={() => safeExecuteCommand("riskyCommand", {})}>
      安全执行命令
    </Button>
  );
};
```

### 3. 命令参数验证

```typescript
// 在智能体配置中添加参数验证
const validateParams = (params: any, schema: any) => {
  // 简单的参数验证逻辑
  for (const [key, type] of Object.entries(schema)) {
    if (typeof params[key] !== type) {
      throw new Error(`参数 ${key} 类型错误，期望 ${type}`);
    }
  }
};

const validatedCommand = {
  name: "updateUserData",
  action: (params) => {
    // 验证参数
    validateParams(params, {
      userId: "string",
      data: "object"
    });

    // 执行命令逻辑
    updateUser(params.userId, params.data);
  }
};
```

## 常见问题

### Q1: 命令没有被执行？

**A:** 检查以下几点：
1. 确认命令名称拼写正确
2. 检查是否在正确的智能体作用域内
3. 确认有组件订阅了该命令
4. 查看浏览器控制台是否有错误信息

### Q2: 如何调试命令执行？

**A:** 使用以下方法：
```typescript
// 添加命令执行日志
const debugRunner = (name: string, params?: any) => {
  console.log(`执行命令: ${name}`, params);
  runner(name, params);
};

// 监听所有命令
commandEmitter.onAny((eventName, ...args) => {
  console.log(`命令事件: ${eventName}`, args);
});
```

### Q3: 命令执行顺序如何保证？

**A:** 命令是异步执行的，如需保证顺序：
```typescript
const sequentialCommands = async () => {
  // 使用 Promise 链保证顺序
  await new Promise(resolve => {
    runner("firstCommand", {});
    setTimeout(resolve, 100); // 等待命令执行
  });

  await new Promise(resolve => {
    runner("secondCommand", {});
    setTimeout(resolve, 100);
  });

  runner("thirdCommand", {});
};
```
