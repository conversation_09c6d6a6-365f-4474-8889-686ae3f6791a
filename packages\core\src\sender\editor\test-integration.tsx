import React, { useRef, useState } from "react";

import Editor, { EditorRef } from "./Editor";

// Simple integration test component to verify the editor works
export const EditorTestComponent: React.FC = () => {
  const [content, setContent] = useState("");
  const editorRef = useRef<EditorRef>(null);

  const handleInsertTag = () => {
    const tagText = `<embedded-tag text="Test Tag" rawValue="test_value" tooltips="This is a test tag"></embedded-tag>`;
    editorRef.current?.insertText(tagText);
  };

  const handleInsertSelect = () => {
    const selectText = `<embedded-select placeholder="Choose option" options="[{&quot;label&quot;:&quot;Option 1&quot;,&quot;value&quot;:&quot;opt1&quot;},{&quot;label&quot;:&quot;Option 2&quot;,&quot;value&quot;:&quot;opt2&quot;}]" defaultValue="" tooltips="Select an option" disabled="false"></embedded-select>`;
    editorRef.current?.insertText(selectText);
  };

  const handleClear = () => {
    editorRef.current?.clear();
  };

  const handleFocus = () => {
    editorRef.current?.focus();
  };

  const handleGetText = () => {
    const text = editorRef.current?.getText();
    alert(`Current text: ${text}`);
  };

  const handleEnterPress = (event: KeyboardEvent) => {
    console.log("Enter pressed:", event);
  };

  return (
    <div style={{ padding: "20px" }}>
      <h3>Slate Editor Test</h3>

      <div style={{ marginBottom: "10px" }}>
        <button onClick={handleInsertTag} style={{ marginRight: "10px" }}>
          Insert Tag
        </button>
        <button onClick={handleInsertSelect} style={{ marginRight: "10px" }}>
          Insert Select
        </button>
        <button onClick={handleClear} style={{ marginRight: "10px" }}>
          Clear
        </button>
        <button onClick={handleFocus} style={{ marginRight: "10px" }}>
          Focus
        </button>
        <button onClick={handleGetText}>Get Text</button>
      </div>

      <div style={{ border: "1px solid #ccc", borderRadius: "4px", padding: "10px" }}>
        <Editor ref={editorRef} onChange={setContent} onEnterPress={handleEnterPress} />
      </div>

      <div style={{ marginTop: "10px" }}>
        <h4>Current Content (HTML):</h4>
        <pre style={{ background: "#f5f5f5", padding: "10px", borderRadius: "4px" }}>{content}</pre>
      </div>
    </div>
  );
};

export default EditorTestComponent;
