/**
 * Request module exports
 *
 * 导出请求模块的所有功能
 */

// 导出请求类
export { Request } from "./request";

// 导出类型
export * from "./types";

// 导出错误处理
export * from "./error";

// 导出拦截器
export * from "./interceptors";

// 导出配置
export * from "./config";

// 创建默认请求实例
import { Request } from "./request";
export const defaultRequestInstance = new Request();

// 导出便捷方法
export const get = defaultRequestInstance.get.bind(defaultRequestInstance);
export const post = defaultRequestInstance.post.bind(defaultRequestInstance);
export const put = defaultRequestInstance.put.bind(defaultRequestInstance);
export const del = defaultRequestInstance.delete.bind(defaultRequestInstance);
export const patch = defaultRequestInstance.patch.bind(defaultRequestInstance);
export const request = defaultRequestInstance.request.bind(defaultRequestInstance);
export const cancelRequest = defaultRequestInstance.cancelRequest.bind(defaultRequestInstance);
export const cancelAllRequests = defaultRequestInstance.cancelAllRequests.bind(defaultRequestInstance);
