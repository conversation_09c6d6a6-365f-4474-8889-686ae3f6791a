{"name": "playground", "version": "1.0.0", "private": true, "description": "", "type": "module", "scripts": {"dev": "vite --force", "build": "vite build", "lint": "eslint ./src --ext .ts,.tsx --fix", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@tailwindcss/vite": "^4.1.4", "antd": "^5.24.3", "dayjs": "^1.11.13", "less": "^4.2.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.3.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@cscs-agent/agents": "workspace:*", "@cscs-agent/core": "workspace:*", "@cscs-agent/icons": "workspace:*", "@cscs-agent/presets": "workspace:*", "@eslint/js": "^9.25.1", "@rollup/plugin-typescript": "^12.1.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "typescript": "^5.8.2", "vite": "^6.2.0"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.9.0"}