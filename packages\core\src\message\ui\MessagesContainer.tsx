import { Flex } from "antd";
import { useEffect, useState } from "react";

import { createMessage } from "@/core/common/message";
import { useActiveConversationId, useIsLoadingMessage, useMessages } from "@/core/state/store";
import { get } from "@/request";
import { MessageStatus, Role } from "@/types";
import { UserOutlined } from "@ant-design/icons";

import Bubble from "./Bubble";

const MessageContainer = () => {
  const [messages, setMessages] = useMessages();
  const [activeConversationId] = useActiveConversationId();
  const [loading, setLoading] = useState(false);
  const [isLoadingMessage] = useIsLoadingMessage();

  useEffect(() => {
    if (activeConversationId) {
      if (!isLoadingMessage) {
        setLoading(true);
        setMessages([]);
      }
      get("/message", {
        conversation_id: activeConversationId,
      })
        .then((res: any) => {
          // TODO any类型
          const list = res.data.data;
          if (Array.isArray(list)) {
            const historyMessages = list.map((i) =>
              createMessage(i.id, i.message_type, i.content, MessageStatus.Finished, i.agent_code, i.user_rating),
            );
            // TODO 优化加载逻辑
            setMessages((messages) => {
              const lastMessage = messages[messages.length - 1];
              if (messages.length === 0 || lastMessage.status !== MessageStatus.Loading) {
                return historyMessages;
              } else {
                return [...messages];
              }
            });
          }
        })
        .catch((error) => {
          if (error.status !== 404) {
            setMessages([]);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [activeConversationId]);

  return (
    <>
      {loading && (
        <div className="ag:flex ag:items-center">
          <div className="ag:animate-spin ag:circle-loader"></div>
        </div>
      )}
      <Flex gap="middle" vertical>
        {messages.map((i) => (
          <Bubble
            key={i.id}
            message={i}
            avatar={{ icon: <UserOutlined />, style: { background: i.role === Role.HUMAN ? "#87d068" : "#3399ff" } }}
            placement={i.role === Role.HUMAN ? "end" : "start"}
            role={i.role}
          />
        ))}
      </Flex>
    </>
  );
};

export default MessageContainer;
