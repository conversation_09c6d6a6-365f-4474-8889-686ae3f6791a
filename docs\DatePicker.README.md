# Slate DatePicker Component

Slate编辑器的DatePicker组件，基于Ant Design的DatePicker组件构建。

## 功能特性

- ✅ 支持日期选择
- ✅ 支持时间选择（可选）
- ✅ 支持自定义日期格式
- ✅ 支持清除功能
- ✅ 支持禁用状态
- ✅ 支持工具提示
- ✅ 与现有的Slate编辑器完全兼容
- ✅ 完整的TypeScript类型支持

## 使用方式

### 1. 使用HTML标签方式插入

```javascript
// 基本日期选择器
const datePickerText = `<embedded-datepicker placeholder="请选择日期" defaultValue="" tooltips="选择一个日期" disabled="false"></embedded-datepicker>`;
editorRef.current?.insertText(datePickerText);

// 带时间的日期选择器
const dateTimePickerText = `<embedded-datepicker placeholder="请选择日期时间" defaultValue="" tooltips="选择日期和时间" disabled="false" format="YYYY-MM-DD HH:mm:ss" showTime="true"></embedded-datepicker>`;
editorRef.current?.insertText(dateTimePickerText);

// 自定义格式的日期选择器
const customFormatText = `<embedded-datepicker placeholder="请选择月份" defaultValue="" tooltips="选择月份" disabled="false" format="YYYY-MM" allowClear="true"></embedded-datepicker>`;
editorRef.current?.insertText(customFormatText);
```

### 2. 使用Slate插件方式

```javascript
import { Transforms } from 'slate';

// 插入基本日期选择器
Transforms.insertNodes(editor, {
  type: 'embedded-datepicker',
  placeholder: '请选择日期',
  defaultValue: '',
  tooltips: '选择一个日期',
  disabled: false,
  format: 'YYYY-MM-DD',
  showTime: false,
  allowClear: true,
  children: [{ text: '' }]
});

// 插入日期时间选择器
Transforms.insertNodes(editor, {
  type: 'embedded-datepicker',
  placeholder: '请选择日期时间',
  defaultValue: '2024-01-01 12:00:00',
  tooltips: '选择日期和时间',
  disabled: false,
  format: 'YYYY-MM-DD HH:mm:ss',
  showTime: true,
  allowClear: true,
  children: [{ text: '' }]
});
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `placeholder` | `string` | `""` | 输入框占位文本 |
| `defaultValue` | `string` | `""` | 默认选中的日期值 |
| `tooltips` | `string` | `""` | 鼠标悬停时显示的提示信息 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `format` | `string` | `"YYYY-MM-DD"` | 日期格式 |
| `showTime` | `boolean` | `false` | 是否显示时间选择 |
| `allowClear` | `boolean` | `true` | 是否显示清除按钮 |

## 日期格式说明

支持的日期格式基于dayjs，常用格式包括：

- `YYYY-MM-DD` - 年-月-日 (2024-01-01)
- `YYYY-MM-DD HH:mm:ss` - 年-月-日 时:分:秒 (2024-01-01 12:00:00)
- `YYYY-MM` - 年-月 (2024-01)
- `MM/DD/YYYY` - 月/日/年 (01/01/2024)
- `DD/MM/YYYY` - 日/月/年 (01/01/2024)

## 示例

### 基本用法

```html
<embedded-datepicker 
  placeholder="请选择日期" 
  defaultValue="" 
  tooltips="选择一个日期" 
  disabled="false">
</embedded-datepicker>
```

### 带时间选择

```html
<embedded-datepicker 
  placeholder="请选择日期时间" 
  defaultValue="2024-01-01 12:00:00" 
  tooltips="选择日期和时间" 
  disabled="false" 
  format="YYYY-MM-DD HH:mm:ss" 
  showTime="true">
</embedded-datepicker>
```

### 月份选择器

```html
<embedded-datepicker 
  placeholder="请选择月份" 
  defaultValue="2024-01" 
  tooltips="选择月份" 
  disabled="false" 
  format="YYYY-MM">
</embedded-datepicker>
```

## 注意事项

1. `defaultValue` 应该符合指定的 `format` 格式
2. 当 `showTime` 为 `true` 时，建议使用包含时间的格式如 `YYYY-MM-DD HH:mm:ss`
3. 组件会自动处理日期值的格式化和验证
4. 选中的日期值会自动更新到 `defaultValue` 属性中，便于序列化和持久化
