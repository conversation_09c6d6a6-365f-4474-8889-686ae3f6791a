@import "tailwindcss" prefix(ag);
@import "./theme/basic.css";
@import "./theme/highlight.css";

/** antdX 覆盖*/
.ant-conversations-label {
  color: var(--agent-text-black-85) !important;
}

.ant-conversations .ant-conversations-group-title {
  padding-left: 12px !important;
}

.ant-conversations .ant-conversations-group-title .ant-typography {
  font-size: 12px !important;
}

.ant-conversations-group-title .ant-typography {
  color: var(--agent-black-65) !important;
}

.ant-conversations-item:hover {
  background-color: rgba(237, 238, 239, 1) !important;
}

.ant-conversations-item-active {
  background-color: rgba(237, 238, 239, 1) !important;
}

.ant-conversations-item-active .ant-conversations-label {
  color: var(--agent-black-85) !important;
}

.ant-sender-actions-btn-disabled {
  background-color: rgba(37, 45, 62, 0.25) !important;
  opacity: 1 !important;
}

.ant-sender {
  background-color: #ffffff;
}

/* markdown table */
.cscs-agent-text-block table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ccc;
}

.cscs-agent-text-block th,
.cscs-agent-text-block td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

.cscs-agent-text-block th {
  background-color: #f2f2f2;
}

.cscs-agent-text-block ol {
  list-style: auto;
  padding-left: 16px;
}

.cscs-agent-text-block ul {
  list-style: initial;
  padding-left: 16px;
}

.ProseMirror {
  word-break: break-all;
}

.ProseMirror-focused {
  outline: none;
}

.cscs-agent-text-block p,
.cscs-agent-text-block ul {
  margin-bottom: 4px;
}

.cscs-agent-text-block p:nth-last-child(1),
.cscs-agent-text-block ul:nth-last-child(1) {
  margin-bottom: 0;
}

.div[id~="dmermaid-"] {
  display: none;
}

.cscs-agent-bubble .cscs-agent-bubble-footer-human {
  visibility: hidden;
}

.cscs-agent-bubble:hover .cscs-agent-bubble-footer-human {
  visibility: visible;
}

/** Sender */
.sender-editor::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.sender-editor::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.sender-editor::-webkit-scrollbar-track {
  background-color: transparent;
}

/** conversation */
.conversation-scrollable::-webkit-scrollbar {
  width: 6px;
  height: 0;
}

.conversation-scrollable::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.2);
}

.conversation-scrollable::-webkit-scrollbar-track {
  background-color: transparent;
}

