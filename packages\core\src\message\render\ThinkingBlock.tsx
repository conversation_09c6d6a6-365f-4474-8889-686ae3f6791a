import { Collapse } from "antd";
import React from "react";
import Markdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

import { BlockStatus } from "@/types";

interface ThinkingBlockProps {
  content: string;
  status: BlockStatus;
}

const ThinkingBlock: React.FC<ThinkingBlockProps> = (props) => {
  const { content, status } = props;

  return (
    <div className="ag:mb-2">
      <Collapse
        size="small"
        defaultActiveKey={["1"]}
        style={{
          width: "100%",
        }}
        items={[
          {
            key: "1",
            label: status === BlockStatus.Loading ? "思考中..." : "思考完成",
            children: (
              <div className="ag:text-gray-400 ag:break-all ag:leading-[1.6em]">
                <Markdown rehypePlugins={[rehypeHighlight, rehypeRaw]} remarkPlugins={[remarkGfm]}>
                  {content}
                </Markdown>
              </div>
            ),
          },
        ]}
      />
    </div>
  );
};

export default ThinkingBlock;
