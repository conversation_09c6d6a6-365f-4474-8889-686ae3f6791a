# Request Module

This module provides a flexible and powerful HTTP request client based on Axios.

## Features

- Configurable base URL, timeout, and headers
- Request and response interceptors
- Error handling
- Request cancellation using AbortSignal
- Request retry

## Usage

### Basic Usage

```typescript
import { Request } from '@agent/core';

// Create a request instance
const request = new Request({
  baseURL: 'https://api.example.com',
  timeout: 10000,
});

// GET request
const data = await request.get('/users');

// POST request
const result = await request.post('/users', { name: 'John', email: '<EMAIL>' });
```

### Request Cancellation

You can cancel requests in two ways:

#### 1. Using AbortController (Recommended)

```typescript
// Create an AbortController
const controller = new AbortController();

// Make a request with the signal
request.get('/users', null, { signal: controller.signal })
  .then(data => console.log(data))
  .catch(error => {
    if (error.name === 'AbortError') {
      console.log('Request was cancelled');
    } else {
      console.error('Request failed:', error);
    }
  });

// Cancel the request
controller.abort('Operation cancelled by user');
```

#### 2. Using Request ID (Legacy)

```typescript
// Make a request with a custom ID
const requestId = 'get-users';
request.get('/users', null, { requestId })
  .then(data => console.log(data))
  .catch(error => console.error(error));

// Cancel the request using the ID
request.cancelRequest(requestId, 'Operation cancelled by user');
```

#### 3. Cancel All Requests

```typescript
// Cancel all pending requests
request.cancelAllRequests('All operations cancelled');
```

### Error Handling

```typescript
// Custom error handler
const request = new Request({
  errorHandler: (error) => {
    console.error('Request failed:', error);
    return Promise.reject(error);
  }
});

// Hide error message for a specific request
request.get('/users', null, { showError: false });
```

## API Reference

### Request Class

The main class for making HTTP requests.

#### Constructor

```typescript
new Request(config?: RequestConfig)
```

#### Methods

- `get<T>(url: string, params?: any, options?: RequestOptions): Promise<T>`
- `post<T>(url: string, data?: any, options?: RequestOptions): Promise<T>`
- `put<T>(url: string, data?: any, options?: RequestOptions): Promise<T>`
- `delete<T>(url: string, params?: any, options?: RequestOptions): Promise<T>`
- `patch<T>(url: string, data?: any, options?: RequestOptions): Promise<T>`
- `request<T>(options: RequestOptions): Promise<T>`
- `cancelRequest(requestId: string, message?: string): void`
- `cancelAllRequests(message?: string): void`
- `getAxiosInstance(): AxiosInstance`
