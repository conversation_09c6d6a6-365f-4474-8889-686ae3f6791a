import "@@/@cscs-agent/core/dist/agent-tailwind.css";
import "@@/@cscs-agent/presets/dist/presets-tailwind.css";
import "@@/@cscs-agent/agents/dist/agents-tailwind.css";
import "@@/@cscs-agent/icons/dist/icons.css";

import "./styles.css";

import { createRoot } from "react-dom/client";
import { RouterProvider } from "react-router";

import { createDefaultRouter, initApp } from "@cscs-agent/core";
import { AgentHome, Chat, Login, defaultAuthGuard } from "@cscs-agent/presets";

import Home from "./pages/home";

initApp({
  loginUrl: "/login",
}).then(() => {
  const router = createDefaultRouter({
    pages: {
      home: {
        Component: Home,
      },
      chat: {
        Component: Chat,
      },
      agentHome: {
        Component: AgentHome,
      },
      login: {
        enable: true,
        Component: Login,
      },
    },
    authGuard: defaultAuthGuard,
  });

  createRoot(document.getElementById("root")!).render(<RouterProvider router={router} />);
});
