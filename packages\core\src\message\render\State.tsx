import { getProperty, setProperty } from "dot-prop";
import React, { useContext, useEffect } from "react";

import { StateUpdateStrategy } from "@/types";

import MessageContext from "../ui/Context";

interface StateProps {
  setList: {
    path: string;
    strategy: StateUpdateStrategy;
    value: any;
  }[];
}

const State: React.FC<StateProps> = (props) => {
  const { setList } = props;
  const messageContext = useContext(MessageContext);
  const setMessageState = messageContext?.setMessageState;

  useEffect(() => {
    if (!setMessageState) return;

    setMessageState((prevValues) => {
      for (const set of setList) {
        const prevValue = getProperty(prevValues, set.path);
        const newValue = set.strategy === StateUpdateStrategy.IncrementalMerge ? prevValue + set.value : set.value;
        setProperty(prevValues, set.path, newValue);
      }
      return structuredClone(prevValues);
    });
  }, [setList]);

  return <div></div>;
};

export default State;
