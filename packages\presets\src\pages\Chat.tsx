import React, { useEffect } from "react";
import { useParams } from "react-router";

import NavigationBar from "@/components/navigation-bar";
import { DefaultChatLayout } from "@/layout";
import {
  BuildInCommand,
  MessageContainer,
  Sender,
  SidePanel,
  useActiveConversationId,
  useCommandRunner,
} from "@cscs-agent/core";

const Chat: React.FC = () => {
  const params = useParams();
  const [, setActiveConversationId] = useActiveConversationId();
  const runner = useCommandRunner();

  useEffect(() => {
    setActiveConversationId(params.id ?? null);

    return () => {
      setActiveConversationId(null);
      runner(BuildInCommand.CancelChatRequest);
    };
  }, [params.id]);

  return (
    <DefaultChatLayout
      navigationBar={<NavigationBar />}
      message={
        <div className="p-4">
          <MessageContainer />
        </div>
      }
      sender={<Sender />}
      sidePanel={
        <div className="pts:bg-gray-100" style={{ height: "100vh" }}>
          <SidePanel />
        </div>
      }
    />
  );
};

export default Chat;
