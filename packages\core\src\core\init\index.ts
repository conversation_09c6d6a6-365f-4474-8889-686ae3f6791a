import {
  DefaultErrorHandlerOption,
  DefaultRequestInterceptorOptions,
  setDefaultErrorHandlerOptions,
  setDefaultRequestInterceptorOptions,
} from "@/request";
import { setBaseUrl } from "@/utils";

import { setToken } from "../common/token";
import { setLoginUrl } from "../common/vars";

interface initAppOptions {
  defaultRequestInterceptorOptions?: Partial<DefaultRequestInterceptorOptions>;
  defaultErrorHandlerOption?: Partial<DefaultErrorHandlerOption>;
  baseUrl?: string;
  loginUrl?: string;
}

export async function initApp(options: initAppOptions = {}) {
  loadTokenFromUrl();
  if (options.defaultRequestInterceptorOptions) {
    setDefaultRequestInterceptorOptions(options.defaultRequestInterceptorOptions);
  }
  if (options.defaultErrorHandlerOption) {
    setDefaultErrorHandlerOptions(options.defaultErrorHandlerOption);
  }
  if (options.baseUrl) {
    setBaseUrl(options.baseUrl);
  }
  if (options.loginUrl) {
    setLoginUrl(options.loginUrl);
  }
}

function loadTokenFromUrl() {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("token");
  if (token) {
    setToken(token);
  }
}
