import { But<PERSON>, Input, <PERSON><PERSON>, Spin } from "antd";
import dayjs from "dayjs";
import React, { useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import { RedoOutlined } from "@ant-design/icons";
import { get, getAssetUrl, useAgentConfigs, useNavigate } from "@cscs-agent/core";
import type { ConversationData, ConversationHistoryResponse } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const ConversationSearch: React.FC = () => {
  const [inputValue, setInputValue] = useState("");
  const [keyword, setKeyword] = useState("");
  const [results, setResults] = useState<ConversationData[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();

  const handleSearch = ($keyword: string, currentPage?: number) => {
    setKeyword($keyword);
    if (!$keyword.trim()) {
      reset();
      return;
    }

    const nextPage = currentPage ? currentPage : page + 1;
    setPage(nextPage);
    if (nextPage === 1) {
      setResults([]);
    }
    setLoading(true);
    get<ConversationHistoryResponse>(`/conversation`, { keyword: $keyword, page: nextPage, size: 10 })
      .then((res) => {
        setResults((prev) => [...prev, ...res.data.data]);
        setHasMore(nextPage < res.data.pagination.page_total);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleSelect = (item: ConversationData) => {
    navigate(`/chat/${item.id}`).then(() => {
      setOpen(false);
    });
  };

  const reset = () => {
    setInputValue("");
    setKeyword("");
  };

  const handleOpenSearchModal = () => {
    setOpen(true);
  };

  const handleClose = () => {
    reset();
    setOpen(false);
  };

  return (
    <div>
      <Button type="text" size="small" icon={<Icon icon="Search" />} onClick={handleOpenSearchModal} />
      <Modal
        open={open}
        onCancel={handleClose}
        title={<div className="pts:-translate-y-1 pts:transform">搜索对话</div>}
        footer={null}
        width={600}
        styles={{
          header: {
            paddingLeft: "24px",
          },
          content: {
            padding: "20px 0 0 0",
            overflow: "hidden",
          },
          body: {
            padding: "24px 0 0 0",
            overflow: "hidden",
            borderTop: "solid 1px rgba(0,0,0,0.09)",
          },
        }}
        mask={false}
      >
        <div className="pts:px-6">
          <Input
            prefix={<Icon icon="Search" style={{ color: "rgba(0,0,0,.25)" }} />}
            placeholder="请输入"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onPressEnter={() => handleSearch(inputValue, 1)}
            allowClear
            className="pts:text-black-85"
          />
        </div>
        <div id="cscs—agent-conversation-search-result-scrollable" className="pts:mt-4 pts:h-80 pts:overflow-y-auto">
          {results.length > 0 ? (
            <InfiniteScroll
              dataLength={results.length}
              next={() => handleSearch(keyword)}
              hasMore={hasMore}
              loader={null}
              scrollableTarget="cscs—agent-conversation-search-result-scrollable"
              style={{ overflow: "hidden" }}
            >
              {results.map((item) => (
                <div
                  key={item.id}
                  className="pts:flex pts:justify-between pts:hover:bg-[rgba(0,0,0,0.03)] pts:px-6 pts:py-3 pts:cursor-pointer"
                  onClick={() => handleSelect(item)}
                >
                  <div className="pts:flex pts:items-center pts:font-medium pts:text-md">
                    <AgentIcon code={item.current_agent_code} className="pts:mr-3" />
                    <Highlight text={item.title} keyword={keyword} />
                  </div>
                  <div className="pts:text-black-65 pts:text-sm">{dayjs(item.updated_at).format("YYYY-MM-DD")}</div>
                </div>
              ))}
            </InfiniteScroll>
          ) : (
            <Empty className="pts:mx-auto pts:mt-10" />
          )}
          <div className="pts:text-center">
            <Spin spinning={loading} indicator={<RedoOutlined spin />} size="small" />
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Empty: React.FC<{
  className?: string;
  style?: React.CSSProperties;
}> = (props) => {
  const { className, style } = props;
  return (
    <div className={className} style={style}>
      <img className="pts:block pts:mx-auto" src={getAssetUrl("/assets/empty.svg")} width="120" />
      <p className="pts:text-black-25 pts:text-center">暂无数据</p>
    </div>
  );
};

const Highlight: React.FC<{
  text: string;
  keyword: string;
}> = (props) => {
  const { text, keyword } = props;
  const safeKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const parts = text.split(new RegExp(`(${safeKeyword})`, "gi"));

  return (
    <span>
      {parts.map((part, i) => (
        <span
          key={i}
          className={part.toLowerCase() === keyword.toLowerCase() ? "pts:text-primary" : "pts:text-black-85"}
        >
          {part}
        </span>
      ))}
    </span>
  );
};

const AgentIcon = (props: { code: string; className?: string }) => {
  const { code, className } = props;
  const agentConfigs = useAgentConfigs();
  const agentConfig = agentConfigs.find((i) => i.code === code);
  const logo = agentConfig?.logo;

  return <img src={logo} className={className} width="16" />;
};

export default ConversationSearch;
