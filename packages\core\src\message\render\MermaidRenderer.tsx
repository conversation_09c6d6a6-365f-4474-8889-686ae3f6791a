import { useDebounceFn } from "ahooks";
import mermaid from "mermaid";
import React, { useEffect, useRef } from "react";

mermaid.initialize({
  startOnLoad: false,
  theme: "default",
  securityLevel: "loose",
  suppressErrorRendering: true,
});

interface MermaidRendererProps {
  code: string;
}

const MermaidRenderer: React.FC<MermaidRendererProps> = ({ code }) => {
  const [diagram, setDiagram] = React.useState<string | null>(null);
  const id = useRef(`mermaid-${Math.random().toString(36).substring(2, 11)}`);

  const { run: renderMermaid } = useDebounceFn(
    () => {
      const content = code.replace(/^```mermaid\n/, "").replace(/\n```$/, "");
      mermaid
        .parse(content, { suppressErrors: true })
        .then((result) => {
          if (!result) {
            return;
          }
          return mermaid.render(id.current, content);
        })
        .then((result) => {
          if (result) {
            setDiagram(result.svg);
          }
        });
    },
    { wait: 100 },
  );

  useEffect(() => {
    renderMermaid();
  }, [code]);

  return (
    <>
      <div className="ag:flex ag:justify-center ag:my-4" hidden={!diagram}>
        <div
          dangerouslySetInnerHTML={{ __html: diagram ?? "" }}
          className="ag:max-w-full ag:overflow-auto"
          style={{ lineHeight: "normal" }}
        />
      </div>
      <pre hidden={!!diagram}>{code}</pre>
    </>
  );
};

export default MermaidRenderer;
