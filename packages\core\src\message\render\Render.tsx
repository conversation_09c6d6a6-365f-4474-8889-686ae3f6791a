import React from "react";

import { Message } from "@/core/common/message";

import PackageRender from "./PackageRender";

interface MessageRenderProps {
  data: Message;
}

/**
 * 解析消息内容
 * 首先依次解析每个 Package，
 */
const MessageRender: React.FC<MessageRenderProps> = (props) => {
  const { data } = props;
  const packages = data.content;

  return (
    <div className="ag:text-sm ag:text-black-85">
      {packages.map((i) => (
        <PackageRender msgPackage={i} key={i.package_id} />
      ))}
    </div>
  );
};

export default MessageRender;
