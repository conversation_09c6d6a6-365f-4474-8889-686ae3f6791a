import { SuggestionConfig } from "@cscs-agent/core";

/**
 * 问答小助手首页推荐问题
 */
export const qaSuggestions: SuggestionConfig[] = [
  {
    name: "动态页面操作列如何配置超链接按钮",
    description: "了解如何在动态页面中配置操作列的超链接按钮",
    content: "动态页面操作列如何配置超链接按钮",
  },
  {
    name: "动态页面如何自定义前端组件",
    description: "了解如何在动态页面中自定义前端组件",
    content: "动态页面如何自定义前端组件",
  },
  {
    name: "动态页面如何配置左右布局",
    description: "了解如何在动态页面中配置左右布局",
    content: "动态页面如何配置左右布局",
  },
  {
    name: "动态页面如何配置新建弹框中的表单",
    description: "了解如何在动态页面中配置新建弹框中的表单",
    content: "动态页面如何配置新建弹框中的表单",
  },
];

/**
 * 生成页面小助手首页推荐问题
 */
export const generatorSuggestions: SuggestionConfig[] = [
  {
    name: "生成企业信息表页面",
    description: "生成一个页面，查询数据库中的企业信息表，在页面列表中展示企业名称、企业性质、统一社会信用代码",
    content: "生成一个页面，查询数据库中的企业信息表，在页面列表中展示企业名称、企业性质、统一社会信用代码",
  },
  {
    name: "生成用户信息表页面（带左右布局）",
    description: "生成一个页面，查询数据库中的用户信息表，在页面列表中展示用户名称、登录账号、所属部门、所属角色。设置所属部门筛选项展示在左侧，当筛选值改变时立即刷新列表数据",
    content: "生成一个页面，查询数据库中的用户信息表，在页面列表中展示用户名称、登录账号、所属部门、所属角色。设置所属部门筛选项展示在左侧，当筛选值改变时立即刷新列表数据",
  },
  {
    name: "生成部门信息表页面（树形结构）",
    description: "生成一个页面，查询数据库中的部门信息表，在页面列表中展示部门id、上级部门id、部门名称、部门代码、创建时间。设置列表为树形，设置【parent_id】字段为id字段的父节点",
    content: "生成一个页面，查询数据库中的部门信息表，在页面列表中展示部门id、上级部门id、部门名称、部门代码、创建时间。设置列表为树形，设置【parent_id】字段为id字段的父节点",
  },
  {
    name: "修改企业信息表页面并添加查询条件",
    description: "我想修改动态页面：将企业信息表-manual-company-basic加载为当前配置对象，设置部门名称作为该页面的查询条件，查询模式为模糊查询",
    content: "我想修改动态页面：将企业信息表-manual-company-basic加载为当前配置对象，设置部门名称作为该页面的查询条件，查询模式为模糊查询",
  },
];

/**
 * 默认推荐问题（生成页面小助手）
 */
export const defaultSuggestions = generatorSuggestions;
