import { Button } from "antd";
import React from "react";

import { ReloadOutlined } from "@ant-design/icons";
import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const Regenerate: React.FC = () => {
  const runner = useCommandRunner();

  const handleRegenerate = () => {
    runner(BuildInCommand.ResendPreviousMessage);
  };

  return <Button type="text" size="small" icon={<ReloadOutlined />} onClick={handleRegenerate} />;
};

export default Regenerate;
