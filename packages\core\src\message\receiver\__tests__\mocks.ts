import { AxiosResponse } from "axios";

import { EventType, MessageChunk, MessagePackageType } from "@/types";

/**
 * 创建模拟的 ReadableStream 读取器
 * @param chunks 要返回的数据块数组
 * @returns 模拟的 ReadableStreamDefaultReader
 */
export function createMockReader(chunks: Uint8Array[]) {
  let index = 0;

  return {
    read: () => {
      return new Promise((resolve) => {
        if (index < chunks.length) {
          resolve({ value: chunks[index++], done: false });
        } else {
          resolve({ value: undefined, done: true });
        }
      });
    },
    cancel: () => Promise.resolve(),
  };
}

/**
 * 创建模拟的 Axios 响应对象
 * @param chunks 要返回的数据块数组
 * @returns 模拟的 AxiosResponse 对象
 */
export function createMockResponse(chunks: Uint8Array[]): AxiosResponse {
  return {
    data: {
      body: {
        getReader: () => createMockReader(chunks),
      },
    },
    status: 200,
    statusText: "OK",
    headers: {},
    config: {} as any,
  };
}

/**
 * 创建 SSE 格式的消息块数据
 * @param chunks 消息块数组
 * @returns Uint8Array 格式的数据
 */
export function createSSEData(chunks: MessageChunk[]): Uint8Array {
  const lines = chunks.map((chunk) => `data: ${JSON.stringify(chunk)}`).join("\n");
  return new TextEncoder().encode(lines);
}

/**
 * 创建测试用的消息块
 * @param packageId 消息包ID
 * @param packageType 消息包类型
 * @param chunkId 块ID
 * @param isLast 是否为最后一个块
 * @param data 块数据
 * @returns 消息块对象
 */
export function createMessageChunk(
  packageId: number,
  packageType: MessagePackageType,
  chunkId: number,
  isLast: boolean,
  data: string,
): MessageChunk {
  return {
    package_id: packageId,
    package_type: packageType,
    chunk_id: chunkId,
    is_last: isLast,
    data,
    event_id: 1001, // 默认为 Loading 事件
    event_type: EventType.Loading,
  };
}

/**
 * 创建带有特定事件类型的测试消息块
 * @param packageId 消息包ID
 * @param packageType 消息包类型
 * @param chunkId 块ID
 * @param isLast 是否为最后一个块
 * @param data 块数据
 * @param eventType 事件类型
 * @param eventId 事件ID
 * @returns 消息块对象
 */
export function createMessageChunkWithEvent(
  packageId: number,
  packageType: MessagePackageType,
  chunkId: number,
  isLast: boolean,
  data: string,
  eventType: EventType,
  eventId: number,
): MessageChunk {
  return {
    package_id: packageId,
    package_type: packageType,
    chunk_id: chunkId,
    is_last: isLast,
    data,
    event_id: eventId,
    event_type: eventType,
  };
}
