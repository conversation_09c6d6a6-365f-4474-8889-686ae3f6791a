# 组件开发指南

## 目录

1. [概述](#概述)
2. [组件系统架构](#组件系统架构)
3. [开发环境准备](#开发环境准备)
4. [创建基础组件](#创建基础组件)
5. [组件注册与配置](#组件注册与配置)
6. [组件通信](#组件通信)
7. [插槽系统](#插槽系统)
8. [高级功能](#高级功能)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

## 概述

CSCS Agent 系统提供了一个强大的组件系统，允许开发者创建可复用的 React 组件来扩展智能体的功能。组件可以嵌入到消息中、显示在侧边面板中，或者作为输入工具使用。

### 核心特性

- **模块化设计**: 组件独立开发，易于维护和复用
- **插槽系统**: 支持在不同位置渲染组件
- **命令集成**: 通过命令系统与其他组件通信
- **动态属性**: 支持运行时属性配置
- **类型安全**: 完整的 TypeScript 支持

### 组件类型

1. **消息组件**: 嵌入在 AI 响应中的交互式组件
2. **插槽组件**: 在特定位置渲染的功能组件
3. **工具组件**: 在输入区域提供辅助功能的组件
4. **侧边面板组件**: 在侧边面板中显示的复杂组件

## 组件系统架构

### 组件发现机制

```typescript
// 组件发现过程
const widgets = useMemo(() => {
  const $widgets = agentConfig?.message?.blocks?.widgets ?? [];
  return [...buildInWidgets, ...$widgets];
}, [agentConfig]);
```

### 组件配置接口

```typescript
export interface WidgetConfig {
  /** 组件唯一标识符 */
  code: string;
  /** 组件描述 */
  description?: string;
  /** React 组件 */
  component: React.FC<any>;
  /** 默认属性 */
  props?: Record<string, any>;
}

export interface MessageSlotWidgetConfig extends WidgetConfig {
  /** 角色过滤 */
  role?: Role;
}
```

### 内置组件

系统提供了几个内置组件作为参考：

```typescript
export const buildInWidgets: WidgetConfig[] = [
  {
    code: "@BuildIn/Tool",
    component: ToolWidget,
  },
  {
    code: "@BuildIn/RadioGroupInteractive",
    component: RadioGroupInteractiveWidget,
  },
  {
    code: "@BuildIn/SelectInteractive",
    component: SelectInteractiveWidget,
  },
];
```

## 开发环境准备

### 项目结构

```
packages/
├── core/                 # 核心框架
│   ├── src/
│   │   ├── widget/      # 内置组件
│   │   ├── types.ts     # 类型定义
│   │   └── ...
├── agents/              # 智能体配置
│   ├── src/
│   │   ├── dynamic-page/
│   │   │   ├── widgets/ # 自定义组件
│   │   │   └── config.tsx
│   │   └── ...
└── presets/             # 预设组件
    ├── src/
    │   ├── components/
    │   └── ...
```

### 依赖安装

```bash
# 安装核心依赖
npm install @cscs-agent/core
npm install @cscs-agent/icons
npm install react react-dom
npm install antd

# 开发依赖
npm install @types/react @types/react-dom
npm install typescript
```

## 创建基础组件

### 1. 简单按钮组件

```typescript
import React from "react";
import { Button } from "antd";
import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

interface SimpleButtonProps {
  title?: string;
  action?: string;
  variant?: "primary" | "default" | "text";
}

const SimpleButton: React.FC<SimpleButtonProps> = (props) => {
  const { title = "点击我", action = "default", variant = "default" } = props;
  const runner = useCommandRunner();

  const handleClick = () => {
    // 触发自定义动作
    if (action === "openSidePanel") {
      runner(BuildInCommand.OpenSidePanel, { width: 400 });
    } else if (action === "insertText") {
      runner(BuildInCommand.InsertTextIntoSender, {
        text: "插入的文本内容"
      });
    }
  };

  return (
    <Button type={variant} onClick={handleClick}>
      {title}
    </Button>
  );
};

export default SimpleButton;
```

### 2. 数据展示组件

```typescript
import React, { useState, useEffect } from "react";
import { Card, List, Spin, message } from "antd";
import { get } from "@cscs-agent/core";

interface DataListProps {
  title?: string;
  apiUrl?: string;
  itemTemplate?: string;
}

interface DataItem {
  id: string;
  name: string;
  description?: string;
}

const DataList: React.FC<DataListProps> = (props) => {
  const { title = "数据列表", apiUrl, itemTemplate } = props;
  const [data, setData] = useState<DataItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (apiUrl) {
      loadData();
    }
  }, [apiUrl]);

  const loadData = async () => {
    try {
      setLoading(true);
      const response = await get<DataItem[]>(apiUrl!);
      setData(response.data || []);
    } catch (error) {
      message.error("数据加载失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title={title} size="small">
      <Spin spinning={loading}>
        <List
          dataSource={data}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                title={item.name}
                description={item.description}
              />
            </List.Item>
          )}
        />
      </Spin>
    </Card>
  );
};

export default DataList;
```

### 3. 交互式表单组件

```typescript
import React, { useState } from "react";
import { Form, Input, Button, message } from "antd";
import { BuildInCommand, useCommandRunner, post } from "@cscs-agent/core";

interface FormWidgetProps {
  title?: string;
  fields?: Array<{
    name: string;
    label: string;
    type: "text" | "textarea" | "number";
    required?: boolean;
  }>;
  submitUrl?: string;
  onSuccess?: (data: any) => void;
}

const FormWidget: React.FC<FormWidgetProps> = (props) => {
  const { title = "表单", fields = [], submitUrl, onSuccess } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const runner = useCommandRunner();

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      
      if (submitUrl) {
        const response = await post(submitUrl, values);
        message.success("提交成功");
        onSuccess?.(response.data);
      } else {
        // 将表单数据插入到发送器
        const formText = Object.entries(values)
          .map(([key, value]) => `${key}: ${value}`)
          .join(", ");
        
        runner(BuildInCommand.InsertTextIntoSender, {
          text: formText
        });
      }
      
      form.resetFields();
    } catch (error) {
      message.error("提交失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      style={{ maxWidth: 400 }}
    >
      <h4>{title}</h4>
      
      {fields.map((field) => (
        <Form.Item
          key={field.name}
          name={field.name}
          label={field.label}
          rules={[
            { required: field.required, message: `请输入${field.label}` }
          ]}
        >
          {field.type === "textarea" ? (
            <Input.TextArea rows={3} />
          ) : field.type === "number" ? (
            <Input type="number" />
          ) : (
            <Input />
          )}
        </Form.Item>
      ))}
      
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};

export default FormWidget;
```

## 组件注册与配置

### 1. 在智能体配置中注册

```typescript
import { AgentConfig } from "@cscs-agent/core";
import SimpleButton from "./widgets/SimpleButton";
import DataList from "./widgets/DataList";
import FormWidget from "./widgets/FormWidget";

export const myAgentConfig: AgentConfig = {
  name: "自定义智能体",
  code: "custom-agent",
  
  // 消息区域组件
  message: {
    blocks: {
      widgets: [
        {
          code: "@Custom/SimpleButton",
          component: SimpleButton,
          props: {
            title: "默认按钮",
            action: "openSidePanel",
            variant: "primary"
          }
        },
        {
          code: "@Custom/DataList",
          component: DataList,
          props: {
            title: "默认数据列表",
            apiUrl: "/api/data"
          }
        }
      ]
    },
    
    // 消息插槽组件
    slots: {
      footer: {
        widgets: [
          {
            code: "@Custom/ActionButton",
            component: SimpleButton,
            role: Role.AI,
            props: {
              title: "操作",
              variant: "text"
            }
          }
        ]
      }
    }
  },
  
  // 发送器区域组件
  sender: {
    slots: {
      headerPanel: {
        widgets: [
          {
            code: "@Custom/FormWidget",
            component: FormWidget,
            props: {
              title: "快速表单",
              fields: [
                { name: "name", label: "名称", type: "text", required: true },
                { name: "description", label: "描述", type: "textarea" }
              ]
            }
          }
        ]
      }
    },
    headerPanel: {
      enable: true,
      height: 300,
      title: "工具面板"
    }
  },
  
  // 侧边面板组件
  sidePanel: {
    render: {
      widgets: [
        {
          code: "@Custom/SidePanelData",
          component: DataList,
          props: {
            title: "侧边面板数据",
            apiUrl: "/api/sidebar-data"
          }
        }
      ]
    }
  },
  
  request: {
    chat: {
      url: "/api/chat"
    }
  }
};
```

### 2. 组件命名约定

```typescript
// 推荐的命名模式
const widgetConfigs = [
  // 模块命名空间
  { code: "@CustomModule/ButtonWidget", component: ButtonWidget },
  { code: "@DataViz/ChartWidget", component: ChartWidget },
  { code: "@Forms/InputWidget", component: InputWidget },
  
  // 内置组件
  { code: "@BuildIn/Tool", component: ToolWidget },
  { code: "@BuildIn/Select", component: SelectWidget },
  
  // 智能体特定组件
  { code: "@DynamicPage/PreviewButton", component: PreviewButton },
  { code: "@QA/AnswerCard", component: AnswerCard }
];
```

## 组件通信

### 1. 使用命令系统

```typescript
import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const CommunicationExample: React.FC = () => {
  const runner = useCommandRunner();

  const handleActions = () => {
    // 发送消息
    runner(BuildInCommand.SendMessage, {
      message: "Hello from component",
      agentCode: "current-agent"
    });

    // 打开侧边面板
    runner(BuildInCommand.OpenSidePanel, { width: 500 });

    // 渲染侧边面板内容
    runner(BuildInCommand.RenderSidePanel, {
      widgetCode: "@Custom/SidePanelWidget",
      widgetProps: { data: "some data" }
    });

    // 插入文本到发送器
    runner(BuildInCommand.InsertTextIntoSender, {
      text: "插入的文本"
    });

    // 插入标签到发送器
    runner(BuildInCommand.InsertTagIntoSender, {
      text: "标签文本",
      rawValue: "标签原始值",
      tooltips: "标签提示"
    });

    // 插入选择器到发送器
    runner(BuildInCommand.InsertSelectIntoSender, {
      placeholder: "请选择",
      options: [
        { label: "选项1", value: "option1" },
        { label: "选项2", value: "option2" }
      ],
      defaultValue: "option1"
    });
  };

  return (
    <Button onClick={handleActions}>
      执行操作
    </Button>
  );
};
```

### 2. 自定义命令

```typescript
// 在智能体配置中定义自定义命令
export const agentConfig: AgentConfig = {
  // ... 其他配置
  commands: [
    {
      name: "customDataRefresh",
      action: (params) => {
        // 自定义命令逻辑
        console.log("刷新数据", params);
        
        // 可以触发其他命令
        runner(BuildInCommand.OpenSidePanel, { width: 400 });
      }
    },
    {
      name: "exportData",
      action: (data) => {
        // 导出数据逻辑
        const blob = new Blob([JSON.stringify(data)], {
          type: "application/json"
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "data.json";
        a.click();
      }
    }
  ]
};

// 在组件中使用自定义命令
const CustomComponent: React.FC = () => {
  const runner = useCommandRunner();

  const handleRefresh = () => {
    runner("customDataRefresh", { timestamp: Date.now() });
  };

  const handleExport = () => {
    const data = { /* 组件数据 */ };
    runner("exportData", data);
  };

  return (
    <div>
      <Button onClick={handleRefresh}>刷新</Button>
      <Button onClick={handleExport}>导出</Button>
    </div>
  );
};
```

## 插槽系统

### 插槽类型

CSCS Agent 系统提供了多种插槽位置来渲染组件：

#### 1. 消息插槽

```typescript
// 消息头部插槽
message: {
  slots: {
    header: {
      widgets: [
        {
          code: "@Custom/MessageHeader",
          component: MessageHeaderWidget,
          role: Role.AI // 只在 AI 消息中显示
        }
      ]
    },
    footer: {
      widgets: [
        {
          code: "@Custom/MessageFooter",
          component: MessageFooterWidget,
          role: Role.AI // 只在 AI 消息中显示
        }
      ]
    }
  }
}
```

#### 2. 发送器插槽

```typescript
// 发送器区域插槽
sender: {
  slots: {
    headerPanel: {
      widgets: [
        {
          code: "@Custom/ToolPanel",
          component: ToolPanelWidget
        }
      ]
    },
    header: {
      widgets: [
        {
          code: "@Custom/QuickTools",
          component: QuickToolsWidget
        }
      ]
    },
    footer: {
      widgets: [
        {
          code: "@Custom/StatusBar",
          component: StatusBarWidget
        }
      ]
    }
  },
  headerPanel: {
    enable: true,
    height: 250,
    title: "工具面板",
    buttonText: "展开工具"
  }
}
```

#### 3. 侧边面板插槽

```typescript
// 侧边面板插槽
sidePanel: {
  slots: {
    header: {
      widgets: [
        {
          code: "@Custom/SidePanelHeader",
          component: SidePanelHeaderWidget
        }
      ]
    },
    footer: {
      widgets: [
        {
          code: "@Custom/SidePanelFooter",
          component: SidePanelFooterWidget
        }
      ]
    }
  },
  render: {
    widgets: [
      {
        code: "@Custom/MainContent",
        component: MainContentWidget
      }
    ]
  }
}
```

### 角色过滤

组件可以根据消息角色进行过滤显示：

```typescript
const RoleSpecificWidget: React.FC = () => {
  return (
    <div>
      <p>这个组件只在 AI 消息中显示</p>
    </div>
  );
};

// 配置中指定角色
{
  code: "@Custom/AIOnly",
  component: RoleSpecificWidget,
  role: Role.AI // 只在 AI 消息中显示
}
```
