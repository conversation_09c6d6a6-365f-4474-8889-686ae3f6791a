import React from "react";

import { getAssetUrl, useActiveAgentConfig } from "@cscs-agent/core";

const Welcome: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <div className="pts:p-4">
      <img width="64px" src={getAssetUrl(agentConfig?.logo)} className="pts:block pts:mx-auto pts:mb-3" />
      <div className="pts:font-bold pts:text-[rgba(37,45,62,0.85)] pts:text-2xl pts:text-center">
        {agentConfig?.welcome}
      </div>
      {agentConfig?.description && (
        <div
          className="pts:bg-white pts:mt-8 pts:p-6 pts:rounded-lg pts:w-[800px] pts:h-[70px] pts:text-[rgba(37,45,62,0.85)] pts:text-sm"
          style={{
            boxShadow: "0px 2px 17px 2px rgba(185,210,246,0.15), 0px 8px 17px -3px rgba(185,210,246,0.1)",
          }}
        >
          {agentConfig?.description}
        </div>
      )}
    </div>
  );
};

export default Welcome;
