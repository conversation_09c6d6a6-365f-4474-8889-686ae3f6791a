import { Button, Toolt<PERSON> } from "antd";
import React from "react";

import { useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import { PresetsCommand } from "../command";

const LogoHeader: React.FC = () => {
  const runner = useCommandRunner();

  const closeSideBar = () => {
    runner(PresetsCommand.CloseSideBar);
  };

  return (
    <div className="pts:h-8 pts:mx-6 pts:my-2 pts:flex pts:items-center pts:justify-between">
      <div className="pts:flex pts:items-center">
        <img src="/assets/logo.png" width="32" />
        <h1 className="pts:text-l pts:ml-3">CSCS AI助手</h1>
      </div>
      <Tooltip title="隐藏侧边栏">
        <Button type="text" size="small" icon={<Icon icon="SideBar" />} onClick={closeSideBar} />
      </Tooltip>
    </div>
  );
};

export default LogoHeader;
