import { Tooltip } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";
import { PresetsCommand } from "@cscs-agent/presets";

interface PreviewButtonProps {
  id: string;
  name: string;
}

const PreviewButton: React.FC<PreviewButtonProps> = (props) => {
  const { id, name } = props ?? {};
  const runner = useCommandRunner();

  const handlePreview = () => {
    runner(BuildInCommand.RenderSidePanel, {
      widgetCode: "@DynamicPage/SidePanelPreview",
      widgetProps: {
        id,
        name,
      },
    });

    runner(BuildInCommand.OpenSidePanel, {
      widgetCode: "@DynamicPage/SidePanelPreview",
      width: 500,
    });

    runner(PresetsCommand.CloseSideBar);
  };

  return (
    <Tooltip title="点击预览">
      <div
        className="ats:mt-1 ats:mb-1 ats:px-3 ats:py-4 ats:flex ats:border ats:rounded-md ats:border-gray-300 ats:w-[300px] ats:cursor-pointer ats:bg-white"
        style={{
          boxShadow: "0px 3px 4px -3px rgba(0,0,0,0.08)",
        }}
        onClick={handlePreview}
      >
        <Icon
          icon="Web"
          style={{
            color: "#6C90F2",
          }}
        />
        <span className="ats:ml-2">{name}</span>
      </div>
    </Tooltip>
  );
};

export default PreviewButton;
