import React from "react";

import { AgentWelcome, PopularQuestion } from "@/components";

interface DefaultAgentLayoutProps {
  navigationBar?: React.ReactNode;
  welcome?: React.ReactNode;
  sender: React.ReactNode;
  popularQuestion?: React.ReactNode;
}

const DefaultAgentLayout: React.FC<DefaultAgentLayoutProps> = (props) => {
  const { sender, welcome, popularQuestion, navigationBar } = props;

  return (
    <div className="pts:flex pts:flex-col pts:bg-[#FBFCFD] pts:w-full pts:h-full">
      {/* Middle content - adaptive width */}
      <div>{navigationBar}</div>
      <div className="pts:flex pts:flex-col pts:flex-1 pts:h-full">
        <div className="pts:flex pts:flex-col pts:flex-1 pts:justify-center pts:items-center pts:overflow-y-auto">
          {welcome ? welcome : <AgentWelcome />}
          {popularQuestion ? popularQuestion : <PopularQuestion />}
        </div>
        <div className="pts:mx-auto pts:mb-4 pts:pb-4 pts:w-[800px]">{sender}</div>
      </div>
    </div>
  );
};

export default DefaultAgentLayout;
